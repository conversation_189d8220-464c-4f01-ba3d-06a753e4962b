import ast
# To run this code you need to install the following dependencies:
# pip install google-genai

import base64
import json
import os
from os.path import exists

import fitz
from google import genai
from google.genai import types
from dotenv import load_dotenv
from typing import Dict, Any, Union, List
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from prompt import PROMPT_EXAMPLE_GEMINI

load_dotenv()


def encode_pdf(pdf_path: str):
    """Encode the pdf to base64."""
    try:
      if not os.path.exists(pdf_path):
        return None
      with open(pdf_path, "rb") as pdf_file:
          return base64.b64encode(pdf_file.read()).decode("utf-8")
    except FileNotFoundError:
        print(f"Error: The file {pdf_path} was not found.")
        return None
    except Exception as e:  # Added general exception handling
        print(f"Error: {e}")
        return None


def get_pages_count(file_path):
    # количество страниц в pdf
    if file_path.lower().endswith('.pdf'):
        doc = fitz.open(file_path)
        pages_count = len(doc)
        doc.close()
        return pages_count
    else:
        # Для изображений всегда 1 страница
        return 1


def get_file_extension(file_path: str) -> str:
    if not exists(file_path):
        print(f"Файл не найден: {file_path}")
        return None

    _, ext = os.path.splitext(file_path)
    return ext.lower().lstrip('.')


def get_mime_type(file_path: str) -> str:
    ext = get_file_extension(file_path)
    if ext == 'pdf':
        return 'application/pdf'
    elif ext in ['png', 'jpg', 'jpeg', 'bmp', 'tiff']:
        return f'image/{ext}'

    return 'text/plain'



def smart_parse(obj):
    if isinstance(obj, (dict, list)):
        return obj  # Уже готовый объект — возвращаем как есть
    elif isinstance(obj, str):
        try:
            return json.loads(obj)
        except json.JSONDecodeError:
            try:
                return ast.literal_eval(obj)
            except (ValueError, SyntaxError):
                raise ValueError("Строка не является валидным JSON или Python-литералом")
    else:
        raise TypeError(f"Неподдерживаемый тип: {type(obj)}")



def clear_text(json_string) -> Dict[str, Any]:
    # Пытаемся распарсить JSON, если json_string вернул строку
    if isinstance(json_string, str):
        try:
            json_string = json_string.strip()

            # Обрабатываем различные форматы JSON
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            if json_string.startswith('"'):
                json_string = json_string[1:]
            if json_string.endswith('"'):
                json_string = json_string[:-1]

            # Преобразуем строку JSON в словарь Python
            extract_data = smart_parse(json_string)
            return extract_data
        except json.JSONDecodeError as e:
            print(f"Не удалось распарсить JSON из extract_info: {e}") # <-- Оригинальный print
            return {}
    return json_string if isinstance(json_string, dict) else {}



def extract_entity_from_page_by_gemini(pdf_path: str, id:int=0):
    if pdf_path.lower().endswith('.pdf'):
        pdf_decoded = encode_pdf(pdf_path)
        if not pdf_decoded:
            print(f"Не удалось закодировать файл: {pdf_path}")
            return None
        data = base64.b64decode(pdf_decoded)
    else:
        # Для изображений
        try:
            with open(pdf_path, "rb") as image_file:
                data = image_file.read()
        except Exception as e:
            print(f"Ошибка чтения файла: {e}")
            return None

    mime_type = get_mime_type(pdf_path)

    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY_PRESTIGE"),
    )

    model =  "gemini-2.5-flash-preview-05-20"

    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_bytes(
                    mime_type=mime_type,
                    data=data,
                ),
                types.Part.from_text(text=PROMPT_EXAMPLE_GEMINI),
            ],
        )
    ]

    # generate_content_config = types.GenerateContentConfig(
    #     response_mime_type="application/json",
    #     response_schema=types.Schema(
    #         type=types.Type.OBJECT,
    #         properties={
    #             "doc": types.Schema(
    #                 type=types.Type.ARRAY,
    #                 items=types.Schema(
    #                     type=types.Type.OBJECT,
    #                     required=["doc_type", "doc_date", "doc_number", "buyer_name", "buyer_code",
    #                             "invoices_numbers", "amount_with_vat", "page_type"],
    #                     properties={
    #                         "id": types.Schema(
    #                             type=types.Type.INTEGER,
    #                             description=f"{id} // НЕ ИЗМЕНЯТЬ!!!"
    #                         ),
    #                         "page_type": types.Schema(
    #                             type=types.Type.INTEGER,
    #                             description="1-лицевая сторона документа, 2-средняя страница в документе, 3-последняя страница документа"
    #                         ),
    #                         "doc_type": types.Schema(
    #                             type=types.Type.STRING,
    #                             enum=["ТТН", "ВН", "АКТ", "ПН", "ДОВ", "ПП", "ПРОЧИЙ"],
    #                             description="ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА-ТТН, ВИДАТКОВА НАКЛАДНА-ВН, АКТ-АКТ, ПРИБУТКОВА НАКЛАДНА-ПН, ДОВЕРЕННОСТЬ-ДОВ, ПОВЕРНЕННАЯ ПОСТАВЩИКУ-ПП, ПРОЧИЙ-ПРОЧИЙ"
    #                         ),
    #                         "doc_number": types.Schema(
    #                             type=types.Type.STRING,
    #                             description="Номер документа"
    #                         ),
    #                         "doc_date": types.Schema(
    #                             type=types.Type.STRING,
    #                             description="Дата в формате dd.mm.yyyy или null"
    #                         ),
    #                         "buyer_name": types.Schema(
    #                             type=types.Type.STRING,
    #                             description="Коротко. Без кавычек и юридического статуса. Например: АШАН/МЕТРО"
    #                         ),
    #                         "buyer_code": types.Schema(
    #                             type=types.Type.INTEGER,
    #                             description="только число 8 или 10 цифр или None"
    #                         ),
    #                         "invoices_numbers": types.Schema(
    #                             type=types.Type.ARRAY,
    #                             items=types.Schema(type=types.Type.INTEGER),
    #                             description="Заполнить только для ТТН: из 'Супровідні документи на вантаж' или из колонки 'Документи з вантажем'. Только уникальные значения. По возрастанию"
    #                         ),
    #                         "amount_with_vat": types.Schema(
    #                             type=types.Type.NUMBER,
    #                             description="У ТТН - Извлеки из 'Усього відпущено на загальну суму' или из колонки 'Загальна сума з ПДВ'. Для всех документов бери только сумму написанную прописью. Переведи в число. Если не смог извлечь, тогда бери 'Усього з ПДВ'. 'У т.ч. ПДВ' - ИГНОРИРУЙ. СУММУ ИЗВЛЕКАЙ СТРОГО ИЗ ДАННОЙ СТРАНИЦЫ!!! СУММЫ ИЗ ДРУГИХ СТРАНИЦ ПЕРЕНОСИТЬ ЗАПРЕЩЕНО!!! Если нет суммы - ставь 0."
    #                         ),
    #                     }
    #                 )
    #             )
    #         }
    #     )
    # )

    try:
        response = client.models.generate_content(model=model, contents=contents)  #, config=generate_content_config)

        if response.usage_metadata:
            print(f'prompt_token_count: {response.usage_metadata.prompt_token_count}')
            print(f'candidates_token_count: {response.usage_metadata.candidates_token_count}')
            print(f"total_token_count: {response.usage_metadata.total_token_count}")
        cleared_text = clear_text(response.text)
        return cleared_text

    except Exception as e:
        print(f"Ошибка при обращении к Gemini API: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")

    pdf_path = r"C:\Rasim\Python\ScanDocument\temp_image\202410 Merge_page_1.png"
    result = extract_entity_from_page_by_gemini(pdf_path, 2558)
    print(result)