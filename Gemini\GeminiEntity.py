# playwright install

import asyncio
from playwright.async_api import async_playwright
import genai
import base64

# Настройка Gemini API (получите ключ: https://ai.google.dev/)
genai.configure(api_key="ВАШ_API_КЛЮЧ")


async def analyze_screenshot(screenshot):
    """Анализирует скриншот через Gemini 1.5 Pro Vision"""
    model = genai.GenerativeModel('gemini-1.5-pro-latest')

    # Конвертируем скриншот в base64
    image_b64 = base64.b64encode(screenshot).decode('utf-8')

    response = model.generate_content(
        [
            "Найди на скриншоте: 1) Цену товара 2) Кнопку 'Купить' (укажи CSS-селектор) 3) Название. Ответ в JSON.",
            {"mime_type": "image/png", "data": image_b64}
        ]
    )
    return response.text


async def auto_order(url, product_name, login=None, password=None):
    """Автоматический заказ товара"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()

        # Шаг 1: Логин (если требуется)
        if login and password:
            await page.goto(f"{url}/login")
            await page.fill('input[name="username"]', login)
            await page.fill('input[name="password"]', password)
            await page.click('button[type="submit"]')
            await page.wait_for_selector("#dashboard", timeout=5000)

        # Шаг 2: Поиск товара
        await page.goto(f"{url}/search?q={product_name}")
        screenshot = await page.screenshot()
        analysis = await analyze_screenshot(screenshot)

        # Парсим ответ Gemini (пример: {"price": "$99", "button": ".buy-btn", "name": "Ноутбук"})
        try:
            import json
            data = json.loads(analysis.strip("```json\n").rstrip("```"))
            print(f"Найден товар: {data['name']}, Цена: {data['price']}")

            # Шаг 3: Добавление в корзину
            await page.click(data["button"])
            await page.wait_for_selector(".cart-notification", timeout=3000)

            # Шаг 4: Оформление заказа
            await page.goto(f"{url}/checkout")
            await page.fill("#address", "ул. Примерная, 123")
            await page.click("#confirm-order")
            print("✅ Заказ оформлен!")

        except Exception as e:
            print(f"❌ Ошибка: {str(e)}")

        await browser.close()


# Запуск
asyncio.run(auto_order(
    url="https://example-shop.com",
    product_name="iPhone 15",
    login="<EMAIL>",  # Опционально
    password="your_password"
))