
# To run this code you need to install the following dependencies:
# pip install google-genai

import base64
import os
from os.path import exists
import sys
import fitz
from google import genai
from google.genai import types
from dotenv import load_dotenv
from pathlib import Path
cur_dir = Path(__file__).resolve()
parent_dir = cur_dir.parent.parent
sys.path.append(str(parent_dir))
from prompt import PROMPT_EXAMPLE_GEMINI_2

load_dotenv()


def encode_pdf(pdf_path: str):
    """Encode the pdf to base64."""
    try:
      if not os.path.exists(pdf_path):
        return None
      with open(pdf_path, "rb") as pdf_file:
          return base64.b64encode(pdf_file.read()).decode("utf-8")
    except FileNotFoundError:
        print(f"Error: The file {pdf_path} was not found.")
        return None
    except Exception as e:  # Added general exception handling
        print(f"Error: {e}")
        return None


def get_pages_count(file_path):
    # количество страниц в pdf
    doc = fitz.open(file_path)
    pages_count = len(doc)
    doc.close()
    return pages_count


def get_file_extension(file_path: str) -> str:
    if not exists(file_path):
        print(f"Файл не найден: {file_path}")
        return None

    _, ext = os.path.splitext(file_path)
    return ext.lower().lstrip('.')


def get_mime_type(file_path: str) -> str:
    ext = get_file_extension(file_path)
    if ext == 'pdf':
        return 'application/pdf'
    elif ext in ['png', 'jpg', 'jpeg', 'bmp', 'tiff']:
        return f'image/{ext}'

    return 'text/plain'


def extract_entity_by_gemini(pdf_path: str):
    pdf_decoded = encode_pdf(pdf_path)
    pages_count = get_pages_count(pdf_path)
    filename_with_extension = os.path.basename(pdf_path).strip()
    mime_type = get_mime_type(pdf_path)
    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )
    # "gemini-2.5-flash-preview-04-17"  # in 0,15$/; out 0,60$. Рассуждение 3.50$; 1,048,576; 65,536. В коде рассуждение не используется
    model =  "gemini-2.5-pro-preview-05-06"  #  "gemini-2.5-flash-preview-05-20"
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_bytes(
                    mime_type= mime_type,  # "application/pdf",
                    data=base64.b64decode(pdf_decoded),
                ),
                types.Part.from_text(text=f"""file_name={filename_with_extension}\n\n{PROMPT_EXAMPLE_GEMINI_2}
                ** {pages_count} страниц тебе даю. Просьба вернуть данные по каждой странице!!! **
                """),
            ],
        )
    ]
    generate_content_config = types.GenerateContentConfig(
        thinking_config = types.ThinkingConfig(
            thinking_budget=0,
        ),
        response_mime_type="application/json",
        response_schema=genai.types.Schema(
            type = genai.types.Type.OBJECT,
            required = [filename_with_extension],
            properties = {
                f"{filename_with_extension}": genai.types.Schema(
                    type = genai.types.Type.ARRAY,
                    items = genai.types.Schema(
                        type = genai.types.Type.OBJECT,
                        required = ["doc_type", "doc_date", "doc_number", "buyer_name", "buyer_code", "invoices_numbers",
                                    "amount_with_vat", "page_number", "page_type"],
                        properties = {
                            "doc_type": genai.types.Schema(
                                type=genai.types.Type.STRING,
                                enum=["ТТН", "ВН", "АКТ", "ПН", "ДОВ", "ПП", "ПРОЧИЙ"],
                                description="""
                                    "ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА"-ТТН,
                                    "ВИДАТКОВА НАКЛАДНА"-ВН,
                                    "АКТ"-АКТ,
                                    "ПРИБУТКОВА НАКЛАДНА"-ПН,
                                    "ДОВЕРЕННОСТЬ"-ДОВ,
                                    "ПОВЕРНЕННАЯ ПОСТАВЩИКУ"-ПП,
                                    "ПРОЧИЙ"-ПРОЧИЙ
                                    """,
                            ),
                            "doc_date": genai.types.Schema( # type: ignore
                                type = genai.types.Type.STRING,
                                description = "Дата в формате dd.mm.yyyy or None",
                            ),
                            "doc_number": genai.types.Schema(
                                type = genai.types.Type.INTEGER,
                                description="только число"
                            ),
                            "buyer_name": genai.types.Schema(
                                type = genai.types.Type.STRING,
                                description="Коротко. Без кавычек и юридического статуса. Например: АШАН/МЕТРО"
                            ),
                            "buyer_code": genai.types.Schema(
                                type = genai.types.Type.INTEGER,
                                description = "только число 8 или 10 цифр"
                            ),
                            "invoices_numbers": genai.types.Schema(
                                type = genai.types.Type.ARRAY,
                                items = genai.types.Schema(
                                    type = genai.types.Type.INTEGER,
                                ),
                                description = "Заполнить только для ТТН: из 'Супровідні документи на вантаж' или из колонки 'Документи з вантажем'. Только уникальные значения. По возрастанию"
                            ),
                            "amount_with_vat": genai.types.Schema(
                                type = genai.types.Type.NUMBER,
                                description="""У ТТН - Извлеки из "Усього відпущено на загальну суму" или из колонки "Загальна сума з ПДВ".
                                            Для всех документов бери только сумму написанную прописью. Переведи в число. 
                                            Если не смог извлечь, тогда бери "Усього з ПДВ"
                                            ** "У т.ч. ПДВ" - ИГНОРИРУЙ.
                                            ** СУММУ ИЗВЛЕКАЙ СТРОГО ИЗ ДАННОЙ СТРАНИЦЫ!!! **
                                            ** СУММЫ ИЗ ДРУГИХ СТРАНИЦ ПЕРЕНОСИТЬ ЗАПРЕЩЕНО!!! **
                                            Если нет суммы - ставь 0.
                                            """
                            ),
                            "page_number": genai.types.Schema(
                                type = genai.types.Type.INTEGER,
                                description = "Номер текущей страницы! Без повторов",
                            ),
                            "page_type": genai.types.Schema(
                                type=genai.types.Type.STRING,
                                enum=["1", "2", "3"],
                                description = """Страница в документе. НЕ ФАЙЛЕ!!!
                                    1-лицевая сторона документа,
                                    2-средняя страница в документе,
                                    3-последняя страница документа.
                                """
                            ),
                        },
                    ),
                ),
                "total_pages_count": genai.types.Schema(
                    type=genai.types.Type.INTEGER,
                    description="Общее количество страниц в документе",
                ),
            },
        ),
    )
    print(f"Количество токенов входящих: {client.models.count_tokens(model=model, contents=contents)}")
    response = client.models.generate_content(model=model, contents=contents, config=generate_content_config)
    print(f'prompt_token_count:{response.usage_metadata.prompt_token_count}')
    print(f'candidates_token_count: {response.usage_metadata.candidates_token_count}')
    print(f"total_token_count: {response.usage_metadata.total_token_count}")
    return response.text


if __name__ == "__main__":
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")

    pdf_path = r"C:\Scan\All\AlreadyAddToDb\2025-04-30_171236.pdf"
    result = extract_entity_by_gemini(pdf_path)
    print(result)