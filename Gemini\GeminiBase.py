import os
import json
from dotenv import load_dotenv
from google import genai
from google.genai import types
from prompt import PROMPT_FLASH, PROMT_EXAMPLE
load_dotenv()

GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
client = genai.Client(api_key=GEMINI_API_KEY)


def clear_text(json_string)->json:
    # Пытаемся распарсить JSON, если json_string вернул строку
    if isinstance(json_string, str):
        import json
        if not json_string: 
            return {}
        
        json_string = json_string.strip()
        try:

            # Обрабатываем различные форматы JSON
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            if json_string.startswith('"'):
                json_string = json_string[1:]
            if json_string.endswith('"'):
                json_string = json_string[:-1]

            # Преобразуем строку JSON в словарь Python
            extract_data = json.loads(json_string)
            print(extract_data)
            return extract_data
        except json.JSONDecodeError as e:
            print(f"Не удалось распарсить JSON из extract_info: {e}")
    return json_string


def extract_data_by_gemini(contents: str, prompt=PROMT_EXAMPLE):
    contents = f"{prompt}\n\nТекст для обработки:\n{contents}"
    model='models/gemini-2.5-pro-preview-03-25'
    # Создаем запрос
    response = client.models.generate_content(model=model, contents=contents,
            config=types.GenerateContentConfig(
            thinking_config=types.ThinkingConfig(thinking_budget=24500)
        ),
    )
    clean_text = clear_text(response.text)
    return clean_text


if __name__ == "__main__":
    response = extract_data_by_gemini("что больше 9.11 или 9.8?")
    print(response)