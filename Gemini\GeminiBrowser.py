import asyncio
from playwright.async_api import async_playwright
from google.generativeai import configure, GenerativeModel
import base64

# Настройка Gemini (получите API-ключ: https://ai.google.dev/)
GEMINI_API_KEY = "ВАШ_API_КЛЮЧ"
configure(api_key=GEMINI_API_KEY)
gemini = GenerativeModel("gemini-pro-vision")

async def analyze_screenshot(screenshot):
    """Анализирует скриншот страницы через Gemini."""
    prompt = """
    Проанализируй скриншот интернет-магазина. Найди:
    1. Цену товара (например: "$99").
    2. Кнопку "Добавить в корзину" (укажи селектор CSS: например, "#add-to-cart").
    3. Название товара.
    Ответ в формате JSON.
    """
    response = await gemini.generate_content_async([prompt, screenshot])
    return response.text

async def auto_order(url, product_name, login=None, password=None):
    """Автоматический заказ товара."""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # Для отладки headless=False
        page = await browser.new_page()

        # Шаг 1: Вход на сайт (если нужен логин)
        if login and password:
            await page.goto(f"{url}/login")
            await page.fill("#username", login)
            await page.fill("#password", password)
            await page.click("#login-button")
            await page.wait_for_selector("#user-profile", timeout=5000)  # Ждем загрузки

        # Шаг 2: Поиск товара
        await page.goto(f"{url}/search?q={product_name}")
        screenshot = await page.screenshot()
        analysis = await analyze_screenshot(screenshot)

        # Парсим ответ Gemini (пример: {"price": "$99", "button": "#add-to-cart", "name": "Кроссовки Nike"})
        try:
            data = eval(analysis)  # Внимание: используйте json.loads в продакшене!
            print(f"Найден товар: {data['name']}, Цена: {data['price']}")

            # Шаг 3: Добавление в корзину
            await page.click(data["button"])
            await page.wait_for_selector("#cart-added", timeout=3000)

            # Шаг 4: Оформление заказа
            await page.goto(f"{url}/checkout")
            await page.fill("#address", "ул. Примерная, 123")
            await page.click("#confirm-order")
            print("Заказ оформлен!")
        except Exception as e:
            print(f"Ошибка: {e}")

        await browser.close()

# Запуск
asyncio.run(auto_order(
    url="https://example.com",
    product_name="Ноутбук",
    login="ваш_логин",  # Опционально
    password="ва