import base64
import os
from google import genai
from google.genai import types
from dotenv import load_dotenv

load_dotenv()

def encode_image(image_path: str):
    """Encode the image to base64."""
    try:
        if not os.path.exists(image_path):
            return None
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode("utf-8")
    except Exception as e:
        print(f"Error encoding image: {e}")
        return None

def get_mime_type(file_path: str) -> str:
    _, ext = os.path.splitext(file_path)
    ext = ext.lower().lstrip('.')
    if ext in ['png', 'jpg', 'jpeg', 'bmp', 'tiff']:
        return f'image/{ext}'
    return 'image/png'

def test_gemini_image():
    """Тест Gemini API с изображением"""
    try:
        image_path = r"temp_image\202410 Merge_page_1.png"
        
        if not os.path.exists(image_path):
            print(f"Файл не найден: {image_path}")
            return
            
        print(f"Обрабатываем изображение: {image_path}")
        
        image_encoded = encode_image(image_path)
        if not image_encoded:
            print("Не удалось закодировать изображение")
            return
            
        mime_type = get_mime_type(image_path)
        print(f"MIME тип: {mime_type}")
        
        client = genai.Client(
            api_key=os.environ.get("GEMINI_API_KEY"),
        )
        
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_bytes(
                        mime_type=mime_type,
                        data=base64.b64decode(image_encoded),
                    ),
                    types.Part.from_text(text="Опиши что ты видишь на этом изображении на русском языке."),
                ],
            )
        ]
        
        model = "gemini-2.0-flash-exp"
        print("Отправляем запрос к Gemini...")
        
        response = client.models.generate_content(
            model=model, 
            contents=contents
        )
        
        print("Ответ получен:")
        print(response.text)
        
    except Exception as e:
        print(f"Ошибка: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gemini_image()
