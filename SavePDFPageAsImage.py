import logging
import os
from pathlib import Path
import psycopg2
import fitz  # PyMuPDF

from DeepSeek.DeepSeek_Old import extract_data_by_deepseek
from Gemini.Gemma import extract_data_from_image_by_gemma, get_correct_text_by_gemma
import asyncio

from Gemini.GoogleDocumentAI import google_ocr_documentai
from Grok.Grok import extract_data_by_grok  # Исправлен импорт
from os.path import exists

from Mistral.MistalCorrectText import extract_data_from_text_by_mistral_sync
from Mistral.MistralOCR import extract_ocr_data_by_mistral
from prompt import PROMPT_CLEAR_TEXT

# Константы
MAX_IMAGE_SIZE_MB = 19
MIN_DPI = 300  # Увеличен минимальный DPI
MAX_DPI = 500
DPI_STEP = 25
MAX_PIXEL_DIMENSION = 10000  # Максимальный размер по любой стороне


# Настройка логирования (из оригинального кода)
logging.basicConfig(
    level=logging.ERROR, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__) # logger используется в оригинале


def get_file_extension(file_path: str) -> str:
    # Получаем расширение файла из пути к файлу.
    return Path(file_path).suffix.lower().lstrip('.')


def calculate_max_dpi_for_dimensions(page_width, page_height, max_pixels=MAX_PIXEL_DIMENSION):
    """
    Вычисляет максимальный DPI, при котором размеры изображения не превысят лимит.
    """
    # Размеры страницы в дюймах (fitz возвращает размеры в пунктах, 1 дюйм = 72 пункта)
    width_inches = page_width / 72
    height_inches = page_height / 72

    # Максимальный DPI для каждого измерения
    max_dpi_width = max_pixels / width_inches
    max_dpi_height = max_pixels / height_inches

    # Берем минимальный из двух
    return min(max_dpi_width, max_dpi_height)


def estimate_image_size_mb(width, height, channels=3):
    """
    Оценивает размер несжатого изображения в МБ.
    Для PNG с хорошим сжатием обычно получается в 2-4 раза меньше.
    """
    uncompressed_size = width * height * channels
    # Консервативная оценка сжатия PNG (коэффициент 2-3)
    estimated_compressed = uncompressed_size / 2.5
    return estimated_compressed / (1024 * 1024)


def save_pdf_page_as_image(file_path: str, page_number: int, dpi: int = 300) -> str:
    """
    Сохраняет указанную страницу PDF как изображение PNG.
    Оптимизирует DPI для соблюдения ограничений:
    - Минимум 300 DPI
    - Максимум 10000 пикселей по любой стороне
    - Размер файла не более 10 МБ
    """
    if get_file_extension(file_path) != 'pdf':
        raise ValueError(f"File {file_path} is not a PDF document.")
    if page_number < 1:
        raise ValueError(f"Page number {page_number} is less than 1.")

    os.makedirs("temp_image", exist_ok=True)
    base_name = f"{Path(file_path).stem}_page_{page_number}.png"
    temp_file_path = os.path.join("temp_image", base_name)

    page_number -= 1  # fitz использует нумерацию с 0
    doc = fitz.open(file_path)

    try:
        if page_number >= len(doc):
            raise ValueError(f"Страница {page_number + 1} отсутствует в документе.")

        page = doc.load_page(page_number)
        page_rect = page.rect
        page_width = page_rect.width
        page_height = page_rect.height

        print(f"Размеры страницы: {page_width:.1f} x {page_height:.1f} пунктов")

        # Вычисляем максимальный DPI для соблюдения лимита размеров
        max_dpi_for_dimensions = calculate_max_dpi_for_dimensions(page_width, page_height)
        print(f"Максимальный DPI для размеров: {max_dpi_for_dimensions:.1f}")

        # Определяем оптимальный DPI
        # Начинаем с минимального допустимого DPI (300)
        optimal_dpi = MIN_DPI

        # Ограничиваем максимальными значениями
        optimal_dpi = min(optimal_dpi, max_dpi_for_dimensions, MAX_DPI)

        # Проверяем, что DPI не меньше минимального
        if optimal_dpi < MIN_DPI:
            print(
                f"ВНИМАНИЕ: Страница слишком большая. Минимальный DPI {MIN_DPI} даст размеры больше {MAX_PIXEL_DIMENSION}px")
            optimal_dpi = MIN_DPI

        # Вычисляем финальные размеры
        final_width = int(page_width * optimal_dpi / 72)
        final_height = int(page_height * optimal_dpi / 72)

        print(f"Выбранный DPI: {optimal_dpi}")
        print(f"Размеры изображения: {final_width} x {final_height} пикселей")

        # Оценка размера файла
        estimated_size = estimate_image_size_mb(final_width, final_height)
        print(f"Оценочный размер файла: {estimated_size:.2f} МБ")

        # Если оценочный размер превышает лимит, уменьшаем DPI
        if estimated_size > MAX_IMAGE_SIZE_MB:
            print("Оценочный размер превышает лимит, уменьшаем DPI...")

            # Вычисляем необходимый коэффициент уменьшения
            scale_factor = (MAX_IMAGE_SIZE_MB / estimated_size) ** 0.5
            new_dpi = int(optimal_dpi * scale_factor)

            # Округляем до ближайшего шага DPI
            new_dpi = (new_dpi // DPI_STEP) * DPI_STEP
            optimal_dpi = max(new_dpi, MIN_DPI)

            final_width = int(page_width * optimal_dpi / 72)
            final_height = int(page_height * optimal_dpi / 72)

            print(f"Скорректированный DPI: {optimal_dpi}")
            print(f"Новые размеры: {final_width} x {final_height} пикселей")

        # Создаем изображение
        mat = fitz.Matrix(optimal_dpi / 72, optimal_dpi / 72)
        pix = page.get_pixmap(matrix=mat)
        pix.save(temp_file_path)

        # Проверяем реальный размер файла
        actual_size_mb = os.path.getsize(temp_file_path) / (1024 * 1024)

        print(f"Финальные параметры:")
        print(f"  DPI: {optimal_dpi}")
        print(f"  Размеры: {final_width} x {final_height} пикселей")
        print(f"  Размер файла: {actual_size_mb:.2f} МБ")

        # Проверка соответствия ограничениям
        if actual_size_mb > MAX_IMAGE_SIZE_MB:
            print(f"ПРЕДУПРЕЖДЕНИЕ: Размер файла {actual_size_mb:.2f} МБ превышает лимит {MAX_IMAGE_SIZE_MB} МБ")

        if final_width > MAX_PIXEL_DIMENSION or final_height > MAX_PIXEL_DIMENSION:
            print(f"ПРЕДУПРЕЖДЕНИЕ: Размеры изображения превышают лимит {MAX_PIXEL_DIMENSION} пикселей")

        return temp_file_path

    finally:
        doc.close()


def extract_pages_from_pdf(pdf_or_image_path, page_numbers:list[int]=None):
    # Извлекает данные из всех страниц PDF файла или указанной страницы или изображения.
    if exists(pdf_or_image_path) is False:
        print(f"File {pdf_or_image_path} does not exist.")

    data_all = []
    if page_numbers:
        for page_number in page_numbers:
            data_all.append(process_image(pdf_or_image_path, page_number))
        doc_all = {'doc': data_all}  # Оборачиваем в список для единообразия
    else:
        doc = fitz.open(pdf_or_image_path)
        for page_number in range(len(doc)):
            data = process_image(pdf_or_image_path, page_number + 1)
            data_all.append(data)  # Добавляем только первый элемент из списка, т.к. process_image возвращает список
        doc.close()
        doc_all = {'doc': data_all}
    return doc_all


# добавляем данные в базу данных
def add_to_db_sync(data):
    """
    Добавляет данные в базу данных синхронно.

    :param data: Данные для добавления в базу данных.
    """
    # Здесь должна быть реализация добавления данных в базу данных
    # Например, использование SQLAlchemy или другого ORM
    sql = f"""
        INSERT INTO t_scan_documents_raw
        (
            full_path,
            page_number,
            description,
            created_at
        )
        VALUES (%s, %s, %s, now())
        ON CONFLICT (file_name, page_number) DO UPDATE
        SET
            description = EXCLUDED.description,
            page_number = EXCLUDED.page_number,
            full_path = EXCLUDED.full_path,
            created_at = now()
        """
    connection_params = {
        'host': os.getenv("PG_HOST_LOCAL", ""),
        'database': os.getenv("PG_DBNAME", ""),
        'user': os.getenv("PG_USER", ""),
        'password': os.getenv("PG_PASSWORD", ""),
        'port': int(os.getenv("PG_PORT", ""))
    }
    values = []
    for doc in data:
        values.append(
            (
            doc['full_path'],
            doc['page_number'],  # Используем 'page_number' если есть, иначе 1
            doc['description']  # Используем 'description' если есть, иначе пустая строка
            )
        )
    conn = psycopg2.connect(**connection_params)
    try:
        cursor = conn.cursor()
        cursor.executemany(sql, values)
        conn.commit()
        cursor.close()

        print(f"Данные OCR успешно занесены в базу")
        return len(values)

    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Ошибка при добавлении данных: {e}")
        return 0
    finally:
        if conn:
            conn.close()


def process_image(pdf_or_image_path, page_number):
    # Обрабатывает изображение, извлекая текст и данные с помощью различных методов OCR.
    # Обработка идет отдельно каждой страницы PDF файла или изображения.
    data = {}
    data['full_path'] = pdf_or_image_path  # file_name = os.path.basename(pdf_or_image_path)
    data['page_number'] = page_number
    data['description'] = None

    # ******** вариант 1 ********
    # Сохраняем страницу как изображение с высоким разрешением, после этого извлекаем текст
    temp_file_name = ''
    if get_file_extension(pdf_or_image_path) in ['png', 'jpg', 'jpeg', 'bmp', 'tiff']:
        temp_file_name = pdf_or_image_path
    elif get_file_extension(pdf_or_image_path) == 'pdf':
        temp_file_name = save_pdf_page_as_image(pdf_or_image_path, page_number)
    else:
        print(f"Не поддерживаемый формат файла для OCR: {get_file_extension(pdf_or_image_path)}")
        return {}

    ocr_text_google = ''
    # ocr_text_google = google_ocr_documentai(temp_file_name)  # Качество хорошее. 1000 стр = 1.5 USD
    # Извлекаем данные из изображения с помощью Mistral
    ocr_text = extract_ocr_data_by_mistral(temp_file_name)  # Качество хорошее. 1000 стр = 1 USD
    if ocr_text is None or ocr_text.strip() == "" or len(ocr_text.strip()) < 1000:
        # Извлекаем данные из изображения с помощью Gemma
        # ocr_text = extract_data_from_image_by_gemma(temp_file_name)  # качество GEMMA - неоднозначное

        # ******** вариант 2 ********
        # Обработка PDF страницы с помощью Google Document AI. Можно передать номер страницы
        ocr_text_google = google_ocr_documentai(temp_file_name) # Качество хорошее. 1000 стр = 1.5 USD
    ocr_text = ocr_text_google if (ocr_text is not None
                                   and ocr_text_google is not None
                                   and len(ocr_text.strip()) <= len(ocr_text_google.strip())) else ocr_text

    # удаляем некорректный текст, повторы символов/слов, аномалии и т.д
    correct_ocr_text = ''
    if ocr_text:
        correct_ocr_text = extract_data_from_text_by_mistral_sync(ocr_text, PROMPT_CLEAR_TEXT)

    get_data = {}
    data['description'] = correct_ocr_text.strip() if correct_ocr_text is not None else None
    if correct_ocr_text is not None and correct_ocr_text.strip() != "":
        if isinstance(get_data, dict) and 'doc' in get_data and get_data['doc']:
            doc_data = get_data.get('doc')
            if doc_data and isinstance(doc_data, list):
                get_data = doc_data[0]
            else:
                # Обработка случая, если 'doc' отсутствует или не является списком
                get_data = data

            data = {**data, **get_data}
    dat_list = [data]
    add_to_db_sync(dat_list)  # Добавляем данные в базу данных синхронно

    # удаляем временный файл
    os.remove(temp_file_name) # удаляем временный файл
    return data


def extract_pdf_files_from_folder(folder_path):
    """
    Извлекает PDF файлы из указанной папки и обрабатывает их.

    :param folder_path: Путь к папке, содержащей PDF файлы.
    :return: Список словарей с данными из каждого PDF файла.
    """
    pdf_files = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))

    for pdf_file in pdf_files:
        file_path = os.path.join(folder_path, pdf_file)
        extract_pages_from_pdf(file_path)


def get_small_data_from_db():
    """
    Отбираем записи из бд, у которых количество символов после OCR распознавания < 1000.
    Повторно передаем их на повторное OCR распознавание.
    """
    sql = """    
        SELECT file_name, page_number
        FROM t_scan_documents_raw
        WHERE id IN (
            SELECT external_id
            FROM t_scan_documents
            WHERE external_id NOT IN  (1231, 1426, 1465, 1491, 1724)
                AND NOT (doc_type = 'АКТ' AND amount_with_vat = 0)
                AND (amount_with_vat = 0 OR COALESCE(doc_type, '') = '')
        )
        ORDER BY
            file_name, 
            page_number
        ;
    """
    connection_params = {
        'host': os.getenv("PG_HOST_LOCAL", ""),
        'database': os.getenv("PG_DBNAME", ""),
        'user': os.getenv("PG_USER", ""),
        'password': os.getenv("PG_PASSWORD", ""),
        'port': int(os.getenv("PG_PORT", ""))
    }
    conn = psycopg2.connect(**connection_params)
    cursor = conn.cursor()
    cursor.execute(sql)
    all_rows = cursor.fetchall()
    for row in all_rows:
        pdf_or_image_path = os.path.join(r"c:\Scan\All\ForParse", row[0])
        if not exists(pdf_or_image_path):
            print(f'Отсутствует файл: {pdf_or_image_path}')
        page_number = row[1]
        print(pdf_or_image_path, page_number)
        extract_pages_from_pdf(pdf_or_image_path, [page_number])


if __name__ == "__main__":

    # Очистка экрана
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")

    # вариант 1. Обработка всех pdf в папке с учетом вложенных
    # folder_path = r"\\PrestigeProduct\Блок 2024\Епіцентр\07_ЛИПЕНЬ 2024р\ТТН"
    # extract_pdf_files_from_folder(folder_path)

    # вариант 2. Обработка одного pdf с указанием списка страниц
    # page_number = [3]  # Номер страницы, которую хотите сохранить как изображение
    # pdf_or_image_file_path = r"c:\Scan\All\ForParse\2025-05-01_120132.pdf"
    # result = extract_pages_from_pdf(pdf_or_image_file_path, page_number)

    # вариант 3. Обработка одного pdf с БЕЗ указания списка страниц
    # result = extract_pages_from_pdf(pdf_or_image_file_path)
    # print(result)

    # вариант 4. Обработка одного pdf с указания страницы. Данные берутся из бд.
    get_small_data_from_db()
