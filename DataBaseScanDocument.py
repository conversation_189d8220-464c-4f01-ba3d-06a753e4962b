# Проходит по всем папкам, в т.ч вложенным, извлекает pdf файлы.
# Извлекает сущности с каждой страницы через OCR распознавание и возвращает JSON -> заносится в таб бд


import json
import pandas as pd
import shutil
import psycopg2
import os
import fitz
import ast
from pathlib import Path
from dotenv import load_dotenv
from typing import Dict, Any, Union, List
from Gemini.GeminiAI import extract_entity_from_page_by_gemini
from Gemini.GeminiEntity import extract_entity_by_gemini
from ProcessingPdf import extract_and_compress_pages

load_dotenv()

connection_params = {
    'host': os.getenv("PG_HOST_LOCAL", ""),
    'database': os.getenv("PG_DBNAME", ""),
    'user': os.getenv("PG_USER", ""),
    'password': os.getenv("PG_PASSWORD", ""),
    'port': int(os.getenv("PG_PORT", ""))
}

TABLE_NAME = "t_scan_all_documents"


def smart_parse(obj):
    if isinstance(obj, (dict, list)):
        return obj  # Уже готовый объект — возвращаем как есть
    elif isinstance(obj, str):
        try:
            return json.loads(obj)
        except json.JSONDecodeError:
            try:
                return ast.literal_eval(obj)
            except (ValueError, SyntaxError):
                raise ValueError("Строка не является валидным JSON или Python-литералом")
    else:
        raise TypeError(f"Неподдерживаемый тип: {type(obj)}")


def clear_text(json_string) -> Dict[str, Any]:
    # Пытаемся распарсить JSON, если json_string вернул строку
    if isinstance(json_string, str):
        try:
            json_string = json_string.strip()

            # Обрабатываем различные форматы JSON
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            if json_string.startswith('"'):
                json_string = json_string[1:]
            if json_string.endswith('"'):
                json_string = json_string[:-1]

            # Преобразуем строку JSON в словарь Python
            extract_data = smart_parse(json_string)
            return extract_data
        except json.JSONDecodeError as e:
            print(f"Не удалось распарсить JSON из extract_info: {e}") # <-- Оригинальный print
            return {}
    return json_string if isinstance(json_string, dict) else {}


def create_index():
    try:
        # Создаем индексы отдельно, без транзакции
        conn = psycopg2.connect(**connection_params)
        conn.autocommit = True  # Включаем автокоммит, чтобы индексы создавались вне транзакции
        cursor = conn.cursor()
        cursor.execute(
            f"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_type_page_file ON {TABLE_NAME}(doc_type, page_type, file_name);")
        cursor.execute(
            f"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_buyer_name ON {TABLE_NAME}(buyer_name) WHERE buyer_code IS NOT NULL;")
        cursor.execute(
            f"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_invoices_gin ON {TABLE_NAME} USING GIN(invoices_numbers);")
        cursor.execute(
            f"CREATE INDEX IF NOT EXISTS idx_buyer_code ON {TABLE_NAME} (buyer_code) WHERE buyer_code IS NOT NULL AND buyer_name IS NOT NULL;")
        cursor.close()
        conn.close()
        print(f"Индексы для таблицы {TABLE_NAME} созданы")
    except Exception as e:
        raise ValueError(f"ERROR DataBaseScanDocument/create_tables: {e}")


def create_table():
    try:
        sql = f"""
            -- Таблица для хранения информации о сканированных документах
            CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
                id SERIAL PRIMARY KEY,
                doc_type varchar(255) NULL,
                doc_date date NULL,
                doc_number varchar(255) NULL,
                buyer_name varchar(255) NULL,
                buyer_code varchar(255) NULL,
                invoices_numbers jsonb NULL,
                amount_with_vat numeric(15,2) NULL DEFAULT 0,
                page_number INT NOT NULL,
                rows_list jsonb NULL,
                page_type INT NULL,
                date_from_1c timestamp(0) NULL,
                file_name varchar(255) NOT NULL,
                full_path varchar(255) NULL,
                description TEXT NULL,
                thinking_content TEXT NULL,
                created_at timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
                CONSTRAINT {TABLE_NAME}_unq UNIQUE (file_name, page_number)
            );

            COMMENT ON TABLE {TABLE_NAME} IS 'Таблица для хранения информации о сканерованных документах';
            COMMENT ON COLUMN {TABLE_NAME}.full_path IS 'Полный путь к файлу';
            COMMENT ON COLUMN {TABLE_NAME}.doc_type IS 'Тип';
            COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Номер';
            COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Дата1C';
            COMMENT ON COLUMN {TABLE_NAME}.description IS 'текст страницы';
            COMMENT ON COLUMN {TABLE_NAME}.created_at IS 'Дата создания записи';
            COMMENT ON COLUMN {TABLE_NAME}.buyer_code IS 'ОКПО';
            COMMENT ON COLUMN {TABLE_NAME}.buyer_name IS 'Покупатель';
            COMMENT ON COLUMN {TABLE_NAME}.date_from_1c IS 'Дата из 1С';
            COMMENT ON COLUMN {TABLE_NAME}.page_type IS 'тип страницы';
            COMMENT ON COLUMN {TABLE_NAME}.rows_list IS 'Список строк на странице';
            COMMENT ON COLUMN {TABLE_NAME}.page_number IS 'Номер страницы в исходном файле';
            COMMENT ON COLUMN {TABLE_NAME}.invoices_numbers IS 'Список номеров связанных ВН';
            COMMENT ON COLUMN {TABLE_NAME}.thinking_content IS 'логика мышления';
            COMMENT ON COLUMN {TABLE_NAME}.file_name IS 'Имя файла';
            COMMENT ON COLUMN {TABLE_NAME}.amount_with_vat IS 'Сумма с НДС';
        """
        result = execute_query(sql)
        print(f"Таблица {TABLE_NAME} создана {result}")
    except Exception as e:
        raise ValueError(f"ERROR DataBaseScanDocument/create_tables: {e}")


def execute_query(query, data=None):
    conn = None
    try:
        conn = psycopg2.connect(**connection_params)
        cursor = conn.cursor()
        if data is None:
            cursor.execute(query)
            if query.strip().lower().startswith("select"):
                result = cursor.fetchall()
            else:
                result = True
        elif isinstance(data, list):
            cursor.executemany(query, data)
            result = None
        else:
            cursor.execute(query, data)
            result = None
        conn.commit()
        cursor.close()
    except Exception as e:
        raise ValueError(f"ERROR DataBaseScanDocument/execute_query: {e}")
    finally:
        if conn:
            conn.close()
    return result


def save_to_database_old(response:dict):
    if not response:
        return 0
    data = []
    try:
        first_key = next(iter(response))  # Получаем первый ключ - имя файла
        items = response[first_key]  # Получаем значение по ключу

        # Проверяем, если items - это словарь с ключом "doc", извлекаем массив
        if isinstance(items, dict) and "doc" in items:
            items = items["doc"]

        for item in items:
            invoices_json = json.dumps(item['invoices_numbers']) if item['invoices_numbers'] is not None else []
            doc_type = item['doc_type'] if item['doc_type'] != 'ПРОЧИЙ' else None
            doc_date = item['doc_date'] if item['doc_date'] is not None  and item['doc_date'] != 'None' else None
            to_data = (
                doc_type ,
                doc_date,
                item['doc_number'],
                item['buyer_name'] if item['buyer_name'] == 'ПРОЧИЙ' else None,
                item['buyer_code'],
                item['page_type'],
                invoices_json,
                item['amount_with_vat'],
                item['page_number'],
                first_key,
                None,
                None,
            )
            data.append(to_data)

        sql_insert = f"""
        INSERT INTO {TABLE_NAME} (
            doc_type,
            doc_date,
            doc_number,
            buyer_name,
            buyer_code,
            page_type,
            invoices_numbers,
            amount_with_vat,
            page_number,
            file_name,
            description,
            thinking_content
        )
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (file_name, page_number) DO UPDATE SET
            doc_type = EXCLUDED.doc_type,
            doc_date = EXCLUDED.doc_date,
            doc_number = EXCLUDED.doc_number,
            buyer_name = EXCLUDED.buyer_name,
            buyer_code = EXCLUDED.buyer_code,
            page_type = EXCLUDED.page_type,
            invoices_numbers = EXCLUDED.invoices_numbers,
            amount_with_vat = EXCLUDED.amount_with_vat,
            description = Null,
            thinking_content = Null,
            created_at = current_timestamp
        """
        # doc_date = COALESCE({TABLE_NAME}.doc_date, EXCLUDED.doc_date),
        # doc_number = COALESCE({TABLE_NAME}.doc_number, EXCLUDED.doc_number),
        # buyer_name = COALESCE({TABLE_NAME}.buyer_name, EXCLUDED.buyer_name),
        # buyer_code = COALESCE({TABLE_NAME}.buyer_code, EXCLUDED.buyer_code),
        # page_type = COALESCE({TABLE_NAME}.page_type, EXCLUDED.page_type),
        # invoices_numbers = COALESCE({TABLE_NAME}.invoices_numbers, EXCLUDED.invoices_numbers),
        # amount_with_vat = COALESCE({TABLE_NAME}.amount_with_vat, EXCLUDED.amount_with_vat),
        # description = COALESCE({TABLE_NAME}.description, EXCLUDED.description),
        # thinking_content = COALESCE({TABLE_NAME}.thinking_content, EXCLUDED.thinking_content),

        execute_query(sql_insert, data)
        print(f"{len(data)} строк сохранены в базу данных")
        return True
    except Exception as e:
        print(f"ERROR DataBaseScanDocument/save_to_database: {e}")
    return False


def save_to_database(response):
    """
    Сохраняет распознанные данные документа в базу данных.
    Функция может обрабатывать два формата входных данных:
    1. Новый формат: {"documents": [{"file_name": ..., "doc": [...]}, ...]}
    2. Старый формат: {"file_name.pdf": [...]}
    """
    if not response:
        print("Получен пустой ответ, сохранение отменено.")
        return False

    data_to_insert = []
    try:
        # Проверяем, является ли это новым форматом со списком документов
        if "documents" in response and isinstance(response.get("documents"), list):
            document_files = response["documents"]
            for file_info in document_files:
                file_name = file_info.get("file_name")
                items = file_info.get("doc", [])

                if not file_name or not items:
                    print(f"Пропуск неполной записи в 'documents': {file_info}")
                    continue

                for item in items:
                    # Добавляем данные из каждого документа в список для вставки
                    invoices_json = json.dumps(item.get('invoices_numbers') or [])
                    doc_type = item.get('doc_type') if item.get('doc_type') != 'ПРОЧИЙ' else None
                    doc_date_raw = item.get('doc_date')
                    doc_date = doc_date_raw if doc_date_raw and str(doc_date_raw).lower() != 'none' else None
                    buyer_name = item.get('buyer_name') if item.get('buyer_name') == 'ПРОЧИЙ' else None

                    to_data = (
                        doc_type,
                        doc_date,
                        item.get('doc_number'),
                        buyer_name,
                        item.get('buyer_code'),
                        item.get('page_type'),
                        invoices_json,
                        item.get('amount_with_vat'),
                        item.get('page_number'),
                        file_name,
                        None,  # description
                        None,  # thinking_content
                    )
                    data_to_insert.append(to_data)

        # Обработка старого формата
        else:
            for file_name, items_raw in response.items():
                items = items_raw
                # Обрабатываем вложенный ключ 'doc' в старом формате
                if isinstance(items_raw, dict) and "doc" in items_raw:
                    items = items_raw.get("doc", [])

                if not isinstance(items, list):
                    print(f"Пропуск ключа '{file_name}', так как значение не является списком документов.")
                    continue

                for item in items:
                    # Добавляем данные из каждого документа в список для вставки
                    invoices_json = json.dumps(item.get('invoices_numbers') or [])
                    doc_type = item.get('doc_type') if item.get('doc_type') != 'ПРОЧИЙ' else None
                    doc_date_raw = item.get('doc_date')
                    doc_date = doc_date_raw if doc_date_raw and str(doc_date_raw).lower() != 'none' else None
                    buyer_name = item.get('buyer_name') if item.get('buyer_name') == 'ПРОЧИЙ' else None

                    to_data = (
                        doc_type,
                        doc_date,
                        item.get('doc_number'),
                        buyer_name,
                        item.get('buyer_code'),
                        item.get('page_type'),
                        invoices_json,
                        item.get('amount_with_vat'),
                        item.get('page_number'),
                        file_name,
                        None,  # description
                        None,  # thinking_content
                    )
                    data_to_insert.append(to_data)

        if not data_to_insert:
            print("Нет данных для сохранения в базу данных.")
            return False

        # SQL-запрос остается неизменным
        sql_insert = f"""
        INSERT INTO {TABLE_NAME} (
            doc_type, doc_date, doc_number, buyer_name, buyer_code, page_type,
            invoices_numbers, amount_with_vat, page_number, file_name,
            description, thinking_content
        )
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (file_name, page_number) DO UPDATE SET
            doc_type = EXCLUDED.doc_type,
            doc_date = EXCLUDED.doc_date,
            doc_number = EXCLUDED.doc_number,
            buyer_name = EXCLUDED.buyer_name,
            buyer_code = EXCLUDED.buyer_code,
            page_type = EXCLUDED.page_type,
            invoices_numbers = EXCLUDED.invoices_numbers,
            amount_with_vat = EXCLUDED.amount_with_vat,
            description = NULL,
            thinking_content = NULL,
            created_at = current_timestamp
        """

        execute_query(sql_insert, data_to_insert)
        print(f"Успешно: {len(data_to_insert)} строк были сохранены в базу данных.")
        return True

    except Exception as e:
        print(f"ERROR DataBaseScanDocument/save_to_database: {e}")
        # Здесь можно добавить более детальное логирование ошибки, например, traceback
        import traceback
        traceback.print_exc()
        return False


def update_buyer_name():
    sql = f"""
    WITH 
    buyers AS (
        SELECT  
            upper(buyer_name) buyer_name, buyer_code, count(*) buyer_name_count
        FROM {TABLE_NAME}
        GROUP BY upper(buyer_name), buyer_code
    ),
    summary AS (
        SELECT 
            buyer_name,
            buyer_code,
            SUM(buyer_name_count) AS total_count
        FROM buyers
        GROUP BY buyer_name, buyer_code
    ),
    ranked_codes AS (
        SELECT 
            buyer_name,
            buyer_code,
            ROW_NUMBER() OVER (
                PARTITION BY buyer_name 
                ORDER BY total_count DESC, buyer_code
            ) AS rn
        FROM summary
    ),
    target_codes AS (
        SELECT 
            buyer_name AS target_name, 
            buyer_code AS target_code
        FROM ranked_codes
        WHERE rn = 1
    )
    UPDATE {TABLE_NAME} AS buyers
    SET buyer_name = tc.target_name
    FROM target_codes tc
    WHERE buyers.buyer_code = tc.target_code
    ;
    """
    result = execute_query(sql)
    if result:
        print(f"Обновление buyer_name завершено")
    else:
        print(f"Обновление buyer_name не завершено")


def update_other():
    result = execute_query(f"""
    UPDATE {TABLE_NAME}
    SET buyer_code = NULL
    WHERE buyer_code = '0' or TRIM(buyer_code) = ''
    ;
    """
    )
    if result:
        print(f"Обновление buyer_code <> 0 завершено")
    else:
        print(f"Обновление buyer_code не завершено")

    result = execute_query(f"""
    UPDATE {TABLE_NAME}
    SET doc_type = NULL 
    WHERE doc_type = 'ПРОЧИЙ' or TRIM(doc_type) = ''
    ;
    """
    )
    if result:
        print(f"Обновление doc_type завершено")
    else:
        print(f"Обновление doc_type не завершено")

    result = execute_query(f"""
    UPDATE {TABLE_NAME}
    SET buyer_name = NULL 
    WHERE buyer_name = 'ПРОЧИЙ' or TRIM(buyer_name) = ''
    ;    
    """)
    if result:
        print(f"Обновление buyer_name завершено")
    else:
        print(f"Обновление buyer_name не завершено")


    sql = f"""
        -- Этот скрипт обновляет столбец invoices_numbers в таблице t_scan_documents.
        -- Он преобразует элементы массива JSONB из строкового типа в числовой.
        -- Например, массив ["9937", "9938", "1135"] будет преобразован в [9937, 9938, 1135].
        
        UPDATE {TABLE_NAME}
        SET
            -- Используем подзапрос для перестройки массива.
            -- 1. jsonb_array_elements_text извлекает все элементы из массива как текст.
            -- 2. value::integer преобразует текстовое значение в число.
            -- 3. jsonb_agg собирает все числовые значения обратно в новый массив JSONB.
            invoices_numbers = (
                SELECT jsonb_agg(value::integer)
                FROM jsonb_array_elements_text(invoices_numbers) AS value
            )
        -- Условие WHERE для безопасности и производительности:
        -- Обновляем только те строки, где массив не пустой и где первый элемент
        -- действительно является строкой. Это предотвращает повторную обработку
        -- уже преобразованных данных и возможные ошибки на пустых массивах.
        WHERE
            invoices_numbers IS NOT NULL
            AND jsonb_array_length(invoices_numbers) > 0
            AND jsonb_typeof(invoices_numbers -> 0) = 'string'
        ;
    """
    result = execute_query(sql)
    if result:
        print(f"Обновление invoices_numbers в число завершено")
    else:
        print(f"Обновление invoices_numbers в число НЕ завершено")


def update_buyer_code():
    sql = f"""
        WITH 
        buyers AS (
            SELECT  
                upper(buyer_name) buyer_name, buyer_code, count(*) buyer_name_count
            FROM {TABLE_NAME}
            GROUP BY upper(buyer_name), buyer_code
        ),
        summary AS (
            SELECT 
                buyer_name,
                buyer_code,
                SUM(buyer_name_count) AS total_count
            FROM buyers
            GROUP BY buyer_name, buyer_code
        ),
        ranked_codes AS (
            SELECT 
                buyer_name,
                buyer_code,
                ROW_NUMBER() OVER (
                    PARTITION BY buyer_name 
                    ORDER BY total_count DESC, buyer_code
                ) AS rn
            FROM summary
        ),
        target_codes AS (
            SELECT 
                buyer_name, 
                buyer_code AS target_code
            FROM ranked_codes
            WHERE rn = 1
        )
        UPDATE {TABLE_NAME} AS buyers
        SET buyer_code = tc.target_code
        FROM target_codes tc
        WHERE buyers.buyer_name = tc.buyer_name
        ;
    """
    result = execute_query(sql)
    if result:
        print(f"Обновление buyer_code завершено")
    else:
        print(f"Обновление buyer_code не завершено")


def get_all_pdf_files(folder_path):
    """Рекурсивно находит все PDF-файлы в папке (включая подкаталоги) и возвращает отсортированный список."""

    # Проверка, что папка существует
    if not os.path.isdir(folder_path):
        raise ValueError(f"Папка не существует: {folder_path}")

    """Возвращает отсортированный список PDF-файлов (регистронезависимо)."""
    pdf_files = [
        str(file)  # Конвертируем Path в строку
        for file in Path(folder_path).rglob("*")
        if file.suffix.lower() == ".pdf"  # Фильтр по расширению
    ]
    return sorted(pdf_files)


def get_pages_count(file_path):
    # количество страниц в pdf
    doc = fitz.open(file_path)
    pages_count = len(doc)
    doc.close()
    return pages_count


def get_pages_from_response(response):
    try:
        if not response:
            return None

        # получаем список страниц из ответа
        first_key = next(iter(response))  # Получаем первый ключ
        pages_count = len(response[first_key])  # Получаем значение по ключу
        return pages_count
    except Exception as e:
        print(f"ERROR DataBaseScanDocument/get_pages_from_response: {e}")
    return None


def is_pages_count_equal_pdf_and_content(file_path, response):
    """Проверяет, равны ли количество страниц в PDF-файле и в ответе Gemini."""
    try:
        pages_count = get_pages_count(file_path)
        response_pages_count = get_pages_from_response(response)
        if pages_count != response_pages_count:
            print(f"Количество страниц в файле {file_path} = {pages_count}, а в ответе = {response_pages_count}")
            return False
        return True
    except Exception as e:
        print(f"ERROR _is_pages_count_equal: {e}")
        return 0


def get_pages_count_from_database(file_name):
    # Вычисляем количество страниц занесено в таб.
    # Если не равно количеству страниц в pdf, тогда обновляем данные в базе данных
    try:
        # Получаем количество страниц из базы данных
        sql = f"SELECT count(*) FROM {TABLE_NAME} WHERE file_name = %s"
        result = execute_query(sql, (file_name,))
        if result:
            return result[0][0]
        return None
    except Exception as e:
        print(f"ERROR get_pages_count_from_database: {e}")
    return None


def is_pages_count_equal_pdf_and_db(file_path):
    """Проверяет, равны ли количество страниц в PDF-файле и в базе данных."""
    try:
        pages_count = get_pages_count(file_path)
        db_pages_count = get_pages_count_from_database(file_path)
        if pages_count != db_pages_count:
            # print(f"Количество страниц в файле {file_path} = {pages_count}, а в базе данных = {db_pages_count}")
            return False
        return True
    except Exception as e:
        print(f"ERROR _is_pages_count_equal: {e}")
        return False


def database_scan_document_main(pdf_folder):
    # главный метод
    pdf_files = get_all_pdf_files(pdf_folder)
    for file_path in  pdf_files:
        print(f"Обработка файла {file_path}")
        try:
            if is_pages_count_equal_pdf_and_db(file_path):
                print(f"Файл {file_path} уже обработан")
                continue

            response = extract_entity_by_gemini(file_path)
            if not response:
                print(f"Файл {file_path} не обработан")
                continue

            # Преобразуем строку в словарь Python
            data_dict = clear_text(response)
            if not is_pages_count_equal_pdf_and_content(file_path, data_dict):
                continue

            save_to_database(data_dict)
            print(f"Файл {file_path} обработан")

            # if save_to_database(data_dict):
                # перемещаем файл в папку "processed"
                # processed_folder = Path(pdf_folder).parent.resolve()
                # processed_folder = os.path.join(processed_folder, "AlreadyAddToDb")
                # if not os.path.exists(processed_folder):
                #     os.makedirs(processed_folder)
                # shutil.move(file_path, os.path.join(processed_folder, os.path.basename(file_path)))
                # print(f"Файл {file_path} перемещен в папку processed")

        except Exception as e:
            print(f"Ошибка при обработке файла {file_path}: {e}")


def extract_not_correct_data_from_db():
    # Извлекаем из ви данные которые некорректно были распознаны OCR.
    # Извлекаем наименования файлов и номера страниц. На их основании будет формироваться новые pdf файлы
    sql = f"""
        SELECT 
            id,
            file_name,
            page_number
        FROM 
            {TABLE_NAME}
        WHERE id NOT IN (2590,2636,3072) 
            AND (doc_type IS NULL
            OR doc_date IS NULL 
            OR buyer_name IS NULL)
        ORDER BY 
            file_name,
            page_number
        ;
    """
    result = execute_query(sql)
    df = pd.DataFrame(result, columns=['id','file_name','page_number'])
    return df


def _extract_pages_():
    import pandas as pd
    # Извлекаем наименования файлов и номера страниц. На их основании будет формироваться новые pdf файлы
    sql = """
        SELECT 
            file_name,
            STRING_AGG(page_number::text, ', ' ORDER BY page_number) AS page_numbers,
            buyer_name,
            concat(doc_type,' ', doc_number,' ', to_char(doc_date,'dd mm yyyy'),'.pdf') save_as
        FROM 
            t_scan_all_documents
        WHERE (file_name NOT ILIKE '%' || doc_type|| '%')
        GROUP BY
            file_name,
            buyer_name,
            doc_type,
            doc_date,
            doc_number
        ORDER BY 
            file_name,
            buyer_name,
            doc_type,
            doc_date,
            doc_number
        ;
    """
    result = execute_query(sql)
    df = pd.DataFrame(result)
    return df


def extract_page(df:pd.DataFrame, pdf_folder:str):

    for i, row in df.iterrows():
        try:
            id = row['id']
            file_name = str(row['file_name'])
            page_number = row['page_number']

            full_path_in = os.path.join(pdf_folder, file_name)
            file_name_out = file_name.replace('.pdf', f' {page_number} .pdf')
            full_path_out = os.path.abspath(os.path.join('temp_image', file_name_out))
            print(f"Обрабатывается файл: {full_path_out}")
            success, stats = extract_and_compress_pages(
                input_path=full_path_in,
                output_path=full_path_out,
                page_numbers=[int(page_number)],  # Только эти страницы попадут в output.pdf
                dpi=300,
                jpeg_quality=95,
            )
            if success:
                print(f"Успешно! Новый файл содержит {stats['processed_pages']} страниц.")
                response = extract_entity_from_page_by_gemini(full_path_out, id)
                if not response:
                    continue

                response_parsed = clear_text(response)
                for item in response_parsed["doc"]:
                    item["page_number"] = page_number
                    for key, value in item.items():
                        if value and ("None" in str(value)
                                      or value == ""
                                      or value == "."
                                      or value == "00.00.0000"
                                      or str(value).lower()=='прочий'):
                            item[key] = None

                response_parsed[file_name] = response_parsed.pop('doc')
                save_to_database(response_parsed)
            else:
                print(f"Ошибка: {stats.get('error')}")
        except Exception as e:
            print(f"Error: extract_page. {e}")

        # удаляем временный файл
        if os.path.exists(full_path_out):
            os.remove(full_path_out)
            print(f"Файл {full_path_out} удален")
        else:
            print(f"Файл {full_path_out} не найден")


if __name__ == "__main__":
    # create_table()
    # create_index()

    # Вначале заменяем "прочее" на Null, т.к. при обновлении идет замена только Null
    update_other()

    # pdf_folder = r"C:\Scan\All\AlreadyAddToDb"  # Папка, где хранятся pdf файлы, которые указаны в бд
    # # database_scan_document_main(pdf_folder)
    # # df = extract_pages()
    # df = extract_not_correct_data_from_db()
    # if not df.empty:
    #     extract_page(df, pdf_folder)

    # загружаем данные из json файла
    with open(r"all_documents.json", "r", encoding="utf-8") as f:
        dicts = json.load(f)

    save_to_database(dicts)
    # update_buyer_name()
    # update_buyer_code()
    # print(f"Обновление buyer_code завершено")
