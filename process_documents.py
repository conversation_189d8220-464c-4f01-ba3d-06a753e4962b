import logging # Оставлено, так как было в оригинале
import asyncio
from decimal import Decimal

import asyncpg
import pandas as pd
import os

from DeepSeek.DeepSeek_Old import extract_data_by_deepseek
from Grok.Grok import extract_data_by_grok
from Grok.GrokAsync import extract_data_by_grok_async
from Mistral.MistalCorrectText import extract_data_from_text_by_mistral_sync
from Mistral.MistralPdfText import get_data_from_text_by_mistral_async
from OpenRouter import extract_data_by_deepseek_r1_async
from UTCdatetime import MODEL_DEEPSEEK
from dotenv import load_dotenv
from Gemini.GeminiFlash import extract_data_by_gemini_flash
# from GeminiBase import extract_data_by_gemini # Закомментировано, т.к. не используется в этом потоке
import psycopg2
import json
from datetime import datetime
from prompt import PROMPT_AMOUNT_WITH_VAT

load_dotenv()
DB_USER = os.getenv("PG_USER", "")
DB_PASSWORD = os.getenv("PG_PASSWORD", "")
DB_HOST = os.getenv("PG_HOST_LOCAL", "")
DB_PORT = os.getenv("PG_PORT", "")
DB_NAME = os.getenv("PG_DBNAME", "")

connection_params = {
    'host': DB_HOST,
    'database': DB_NAME,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'port': int(DB_PORT)
}


if not all([DB_USER, DB_PASSWORD, DB_HOST, DB_PORT, DB_NAME]):
    raise ValueError("Не все параметры подключения заданы в .env файле")

# Настройка логирования (из оригинального кода)
logging.basicConfig(
    level=logging.ERROR, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__) # logger используется в оригинале
token_count_global = []

"""
Количество одновременно обрабатываемых пакетов (LLM вызовов).
Перевод рассуждения DeepSeek подвязан под ограничениями Gemini API. 
Поэтому в минуту не более 15 запросов с учетом что параллельно может где-то тоже обрабатывать
Используем MODEL_DEEPSEEK == "deepseek-reasoner" - перевод рассуждения.
"""
if MODEL_DEEPSEEK == "deepseek-chat":  # "deepseek-reasoner" - перевод рассуждения.
    PARALLEL_PROCESSING_LIMIT = 10
else:
    PARALLEL_PROCESSING_LIMIT = 10

# если время UTC >= 16:30 и меньше 12:30 тогда
# тогда MODEL_DEEPSEEK = "deepseek-reasoner"

# Константы для обработки
# Количество записей/документов на один вызов LLM. Учитывать лимит 65000
RECORDS_PER_LLM_CALL = 5

    
async def get_db_pool():
    return await asyncpg.create_pool(
        user=DB_USER,
        password=DB_PASSWORD,
        host=DB_HOST,
        port=DB_PORT,
        database=DB_NAME
    )


async def table_exists(pool, table_name):
    try:
        async with pool.acquire() as conn:
            exists = await conn.fetchval(
                """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = $1
                )
                """,
                table_name
            )
            print(f"Таблица {table_name} существует: {exists}") # Оригинальный print
            return exists
    except Exception as e:
        logger.error(f"Ошибка при проверке существования таблицы: {e}") # Оригинальный logger
        # В оригинале ValueError выбрасывался здесь, но это может быть неоптимально
        # для асинхронного потока. Лучше обработать выше.
        return False # Изменено, чтобы не прерывать поток сразу, а дать вызывающей функции решить


async def delete_records_from_t_scan_documents(pool):
    """Удаляет записи из t_scan_documents, у которых нет соответствия в t_scan_documents_raw"""
    sql = """
        DELETE FROM t_scan_documents
        WHERE id IN (
            SELECT doc.id
            FROM t_scan_documents AS doc
                LEFT JOIN t_scan_documents_raw AS raw
                    ON raw.id = doc.external_id
            WHERE raw.id IS NULL
        );
    """
    record_count = await run_sql(pool, sql)
    logger.info(f"Удалено записи из t_scan_documents, у которых нет соответствия в t_scan_documents_raw: {record_count}")


async def update_buyer_name_upper(pool):
    # buyer_name -> в верхний регистр 
    sql = """
        UPDATE t_scan_documents AS upd
        SET buyer_name = upper(buyer_name)
        ;
    """
    record_count = await run_sql(pool, sql)
    logger.info(f"Обновлено update_doc_ttn_from_vn: {record_count}")
    return True


async def update_doc_ttn_from_vn(pool):
    # ТТН page_type=1 заполняется из ВН
    sql = """
        UPDATE public.t_scan_documents AS upd
        SET 
            doc_date = COALESCE(upd.doc_date, t1.doc_date),
            doc_number = COALESCE(NULLIF(upd.doc_number, ''), NULLIF(t1.doc_number, '')),
            buyer_name = COALESCE(NULLIF(upd.buyer_name, ''), NULLIF(t1.buyer_name, '')),
            buyer_code = COALESCE(NULLIF(upd.buyer_code, ''), NULLIF(t1.buyer_code, ''))
        FROM public.t_scan_documents AS t1
        WHERE 
            -- Условия для ТТН
            upd.doc_type = 'ТТН' 
            AND upd.page_type = 1
            -- Условия для ВН
            AND t1.doc_type = 'ВН'
            -- Связь: один из номеров ТТН.invoices_numbers = ВН.doc_number
            AND t1.doc_number::text = ANY(
                SELECT jsonb_array_elements_text(upd.invoices_numbers)
            )
            -- Связь: одинаковый путь к файлу
            AND upd.file_name = t1.file_name
    ;
    """
    record_count = await run_sql(pool, sql)
    logger.info(f"Обновлено update_doc_ttn_from_vn: {record_count}")
    return True


async def update_doc_vn_from_ttn(pool):
    # ВН заполняется из ТТН page_type=1 (шапки ТТН)
    sql = """
        UPDATE public.t_scan_documents AS upd
        SET 
            doc_date = COALESCE(upd.doc_date, t1.doc_date),
            buyer_name = COALESCE(NULLIF(upd.buyer_name, ''), NULLIF(t1.buyer_name, '')),
            buyer_code = COALESCE(NULLIF(upd.buyer_code, ''), NULLIF(t1.buyer_code, ''))
        FROM public.t_scan_documents AS t1
        WHERE 
            -- Условия для ВН
            upd.doc_type = 'ВН'
            -- Условия для ТТН
            AND t1.doc_type = 'ТТН' 
            AND t1.page_type = 1
            -- Связь: ТТН.invoices_numbers содержит ВН.doc_number
            AND upd.doc_number::text = ANY(
                SELECT jsonb_array_elements_text(t1.invoices_numbers)
            )
            -- Связь: одинаковый путь к файлу
            AND upd.file_name = t1.file_name
    ;
    """
    record_count = await run_sql(pool, sql)
    logger.info(f"Обновлено update_doc_ttn_from_vn: {record_count}")
    return True


async def update_doc_ttn_from_ttn(pool):
    # ТТН page_type=3 или 999 заполняется из ТТН page_type=1
    sql = """
        UPDATE public.t_scan_documents AS upd
        SET 
            doc_date = COALESCE(upd.doc_date, t1.doc_date),
            doc_number = COALESCE(NULLIF(upd.doc_number, ''), NULLIF(t1.doc_number, '')),
            buyer_name = COALESCE(NULLIF(upd.buyer_name, ''), NULLIF(t1.buyer_name, '')),
            buyer_code = COALESCE(NULLIF(upd.buyer_code, ''), NULLIF(t1.buyer_code, ''))
        FROM public.t_scan_documents AS t1
        WHERE 
            -- Условия для целевых ТТН
            upd.doc_type = 'ТТН' 
            AND upd.page_type IN (3, 999)
            -- Условия для исходных ТТН
            AND t1.doc_type = 'ТТН' 
            AND t1.page_type = 1
            -- Связь: одинаковые invoices_numbers (сравнение JSONB)
            AND upd.invoices_numbers = t1.invoices_numbers
            -- Связь: одинаковый путь к файлу
            AND upd.file_name = t1.file_name
            ;
    """
    record_count = await run_sql(pool, sql)
    logger.info(f"Обновлено doc_number, buyer_name, buyer_code у незаполненных ТТН, как правило у последних страниц: {record_count}")
    return True


async def run_sql(pool, sql, params=None): # Эта функция уже была async
    try:
        async with pool.acquire() as conn:
            if params is None:
                result_count = await conn.execute(sql)
            else:
                result_count = await conn.execute(sql, (params,))
            return result_count
    except Exception as e:
        logger.error(f"Ошибка при обновлении данных других записей: {e}") # Оригинальный logger
    return 0


def get_description_by_id(id):
    sql = """
        SELECT description
        FROM t_scan_documents_raw
        WHERE id = $1
    """
    result = asyncio.run(run_sql(sql, id))
    return result

async def create_contents(df_batch: pd.DataFrame): # df переименован в df_batch для ясности
    if df_batch.empty:
        # logger.info("Пустой пакет документов") # Оригинальный logger, но здесь print не было
        return ""

    contents = '*' * 5
    # Формируем JSON-подобные строки для каждой записи, как в предыдущих обсуждениях
    # Это более надежно, чем прямая конкатенация, если LLM ожидает структуру.
    # Если LLM ожидает просто конкатенацию "id: ..., CONTENT: ...", то можно вернуть к тому варианту.
    # Оставляю более структурированный вариант, как более предпочтительный.
    doc_parts = []
    ids = []
    for _, row in df_batch.iterrows():
        id = row['id']
        ids.append(id)  # Собираем ID для отладки
        page_number = row['page_number']
        file_name = row['file_name']
        description = row['description']
        print(f"Обработка документа ID {id}, страница {page_number}, файл {file_name}") # Оригинальный print
        # Ключи "id" и "CONTENT" взяты из оригинального кода
        # Используем json.dumps для корректного экранирования description
        doc_parts.append(f'{{"id" : {id}, "CONTENT": {json.dumps(description, ensure_ascii=False, indent=2)}}}')
    
    contents += "\n" + "\n".join(doc_parts) # Разделяем записи новой строкой
    return contents, ids


async def add_to_db_async(pool, result: dict):
    if not result or not result.get("doc"):
        print("Нет данных для добавления")
        return 0

    docs = []
    for row in result['doc']:
        if 'id' not in row or row['id'] is None:
            logger.warning(f"Пропущен документ из-за отсутствия 'id': {row}")
            continue

        processed = {
            'external_id': row['id'],
            'page_type': row.get('page_type') if row.get('page_type', None) is not None else None,  # 1,2,3,999
            'doc_type': row.get('doc_type', None),
            'doc_date': datetime.strptime(row.get('doc_date'), '%d.%m.%Y').date() if row.get('doc_date', None) is not None else None,
            'doc_number': str(row.get('doc_number')) if row.get('doc_number', None) is not None else None,
            'buyer_name': row.get('buyer_name', None),
            'buyer_code': str(row.get('buyer_code')) if row.get('buyer_code', None) is not None else None,
            'invoices_numbers': json.dumps(row.get('invoices_numbers', [])),
            'rows_list': json.dumps(row.get('rows_list', [])),
            'thinking_content': row.get('thinking_content', None),
            'amount_with_vat': Decimal(row.get('amount_with_vat', 0)) if row.get('amount_with_vat', None) is not None else Decimal('0')
        }
        docs.append(processed)

    if not docs:
        logger.warning("Нет валидных документов для добавления в БД после фильтрации.")
        return 0

    conn = await pool.acquire()
    try:
        values = [tuple(value.values()) for value in docs]
        await conn.executemany("""
            INSERT INTO t_scan_documents (
                external_id, page_type, doc_type, doc_date, doc_number,
                buyer_name, buyer_code, invoices_numbers, rows_list,
                thinking_content, amount_with_vat, created_at
            )
            VALUES (
                $1, $2::integer, $3::text, $4::date, $5::text, 
                $6::text, $7::text, $8::jsonb, $9::jsonb, 
                $10::text, $11::numeric, now()
            )
            ON CONFLICT (external_id) 
            DO UPDATE SET
                page_type = EXCLUDED.page_type,
                doc_type = EXCLUDED.doc_type,
                doc_date = EXCLUDED.doc_date,
                doc_number = EXCLUDED.doc_number,
                buyer_name = EXCLUDED.buyer_name,
                buyer_code = EXCLUDED.buyer_code,
                invoices_numbers = EXCLUDED.invoices_numbers,
                rows_list = EXCLUDED.rows_list,
                thinking_content = EXCLUDED.thinking_content,
                amount_with_vat = EXCLUDED.amount_with_vat,
                created_at = now()
        """, values)

        print(f"Успешно вставлено/обновлено: {len(values)} записей: {[i[0] for i in list(values)]}")
        return len(values)

    except Exception as e:
        logger.error(f"Ошибка при добавлении данных: {e}")
        return 0
    finally:
        await pool.release(conn)


def add_to_db_sync(result: dict, connection_params = connection_params):
    if not result or not isinstance(result, dict) or not result.get("doc"):
        print("Нет данных для добавления")
        return 0

    docs = []
    for row in result['doc']:
        if 'id' not in row or row['id'] is None:
            continue

        processed = {
            'external_id': row['id'],
            'page_type': row.get('page_type'),
            'doc_type': row.get('doc_type'),
            'doc_date': row.get('doc_date'),
            'doc_number': row.get('doc_number'),
            'buyer_name': row.get('buyer_name'),
            'buyer_code': row.get('buyer_code'),
            'invoices_numbers': row.get('invoices_numbers', []),
            'rows_list': row.get('rows_list', []),
            'thinking_content': row.get('thinking_content'),
            'amount_with_vat': row.get('amount_with_vat')
        }
        docs.append(processed)

    if not docs:
        return 0
        
    conn = None
    try:
        conn = psycopg2.connect(**connection_params)
        cursor = conn.cursor()
        
        values_list = []
        for doc in docs:
            external_id = int(doc['external_id'])
            page_type = str(doc['page_type']) if doc['page_type'] is not None else None
            
            doc_date_obj = None
            if doc['doc_date']:
                try:
                    doc_date_obj = datetime.strptime(str(doc['doc_date']), "%d.%m.%Y").date()
                except (ValueError, TypeError):
                    doc_date_obj = None
            
            buyer_code = str(doc['buyer_code']) if doc['buyer_code'] is not None else None
            doc_number_val_str = str(doc['doc_number']) if doc['doc_number'] is not None else None
            amount_with_vat = 0

            # Если сумма с НДС пустая. Извлекаем ее чз ИИ.
            if doc['amount_with_vat'] is None or doc['amount_with_vat'] == 0:
                description = get_description_by_id(external_id)
                amount_with_vat = extract_data_from_text_by_mistral_sync(description, PROMPT_AMOUNT_WITH_VAT)

            values = (
                external_id,
                page_type,
                str(doc['doc_type']) if doc['doc_type'] is not None else None,
                doc_date_obj,
                doc_number_val_str,
                str(doc['buyer_name']) if doc['buyer_name'] is not None else None,
                buyer_code,
                json.dumps(doc.get('invoices_numbers', [])),
                json.dumps(doc.get('rows_list', [])),
                str(doc['thinking_content']) if doc['thinking_content'] is not None else None,
                str(doc['amount_with_vat']) if doc['amount_with_vat'] is not None else amount_with_vat
            )
            values_list.append(values)

        if not values_list:
            return 0

        # Мультивставка
        cursor.executemany("""
            INSERT INTO t_scan_documents (
                external_id, page_type, doc_type, doc_date, doc_number,
                buyer_name, buyer_code, invoices_numbers, rows_list,
                thinking_content, amount_with_vat, created_at
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
            ON CONFLICT (external_id) DO UPDATE 
            SET 
                page_type = CASE WHEN EXCLUDED.page_type IS NULL THEN t_scan_documents.page_type ELSE EXCLUDED.page_type END,
                doc_type = CASE WHEN EXCLUDED.doc_type IS NULL THEN t_scan_documents.doc_type ELSE EXCLUDED.doc_type END,
                doc_date = CASE WHEN EXCLUDED.doc_date IS NULL THEN t_scan_documents.doc_date ELSE EXCLUDED.doc_date END,
                doc_number = CASE WHEN EXCLUDED.doc_number IS NULL THEN t_scan_documents.doc_number ELSE EXCLUDED.doc_number END,
                buyer_name = CASE WHEN EXCLUDED.buyer_name IS NULL THEN t_scan_documents.buyer_name ELSE EXCLUDED.buyer_name END,
                buyer_code = CASE WHEN EXCLUDED.buyer_code IS NULL THEN t_scan_documents.buyer_code ELSE EXCLUDED.buyer_code END,
                invoices_numbers = CASE WHEN EXCLUDED.invoices_numbers::text = '[]' AND t_scan_documents.invoices_numbers::text <> '[]' THEN t_scan_documents.invoices_numbers ELSE EXCLUDED.invoices_numbers END,
                rows_list = CASE WHEN EXCLUDED.rows_list::text = '[]' AND t_scan_documents.rows_list::text <> '[]' THEN t_scan_documents.rows_list ELSE EXCLUDED.rows_list END,
                thinking_content = EXCLUDED.thinking_content,
                amount_with_vat = EXCLUDED.amount_with_vat
        """, values_list)
        
        conn.commit()
        cursor.close()
        
        print(f"Успешно обработано: {len(values_list)} записей")
        return len(values_list)
        
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Ошибка при добавлении данных: {e}")
        return 0
    finally:
        if conn:
            conn.close()


async def get_all_documents_raw(pool):
    """Получает все записи из t_scan_documents_raw, которые еще не обработаны"""

    await delete_records_from_t_scan_documents(pool)

    try:
        if not await table_exists(pool, 't_scan_documents_raw'):
            # Вместо ValueError здесь, лучше обработать на уровне main, чтобы не прерывать пул
            logger.error("Таблица t_scan_documents_raw не существует. Обработка невозможна.")
            return pd.DataFrame()  # Возвращаем пустой DataFrame

        url_full = """
            SELECT id, file_name, page_number, description
            FROM t_scan_documents_raw
            WHERE id IN (
                SELECT external_id
                FROM t_scan_documents
                WHERE external_id NOT IN  (1231, 1426, 1465, 1491, 1724)
                    AND NOT (doc_type = 'АКТ' AND amount_with_vat = 0)
                    AND (amount_with_vat = 0 OR COALESCE(doc_type, '') = '')
            )
            ORDER BY
                file_name, 
                page_number
            ;
        """

        async with pool.acquire() as conn:
            records = await conn.fetch(url_full)
            df = pd.DataFrame(records, columns=['id', 'file_name', 'page_number', 'description'])
            print(f"Получено {len(df)} записей из t_scan_documents_raw")
            logger.info(f"Получено {len(df)} записей из t_scan_documents_raw")
            return df
    except Exception as e:
        logger.error(f"Ошибка при получении документов: {e}")  # Оригинальный logger
        return pd.DataFrame()  # Возвращаем пустой DataFrame при ошибке


async def process_one_batch_async(pool, df_batch_for_llm: pd.DataFrame, semaphore: asyncio.Semaphore):
    """
    Асинхронно обрабатывает один пакет записей: вызывает LLM и сохраняет в БД.
    """
    global token_count_global
    async with semaphore: # Ограничиваем количество одновременных вызовов этой функции
        if df_batch_for_llm.empty:
            return # Нечего обрабатывать

        first_id = df_batch_for_llm.iloc[0]['id'] # Для идентификации пакета в логах/print
        # print(f"[{datetime.now()}] Начало обработки пакета с ID {first_id}") # Отладочный print, можно убрать

        contents, ids_original = await create_contents(df_batch_for_llm)
        if not contents:
            # logger.warning(f"Пустой контент для LLM для пакета с ID {first_id}, пропуск.")
            return
        # result, token_count = await extract_data_by_deepseek(contents, model = "deepseek-chat" )  # model=MODEL_DEEPSEEK
        # token_count_global.append(token_count)
        # result = await extract_data_by_deepseek_r1_async(contents)
        # result = await extract_data_by_gemini_flash(contents)
        # result = await get_data_from_text_by_mistral_async(contents)
        # result = await extract_data_by_grok(contents)
        result = await extract_data_by_grok_async(contents)
        # print(result)
        if result:
            # Проверка на совпадение ID в контенте с оригинальными ID. Т.к. ИИ может иногда возвращать не те ID
            ids_content = [i['id'] for i in result['doc']]
            if ids_original != ids_content:
                # logger.warning(f"IDs в контенте не совпадают с оригинальными для пакета с ID {first_id}. Оригинальные: {ids_original}, в контенте: {ids_content}")
                await process_one_batch_async(pool, df_batch_for_llm, semaphore)
            else:
                add_to_db_sync(result)
                # await add_to_db_async(pool, result)


async def main():
    global token_count_global
    pool = await get_db_pool()
    try:
        df_all_docs = await get_all_documents_raw(pool)

        if df_all_docs.empty:
            logger.info("Нет документов для обработки.")
            return

        # Семафор для ограничения количества одновременно выполняемых задач обработки пакетов
        semaphore = asyncio.Semaphore(PARALLEL_PROCESSING_LIMIT)

        tasks = []
        num_total_records = len(df_all_docs)

        for i in range(0, num_total_records, RECORDS_PER_LLM_CALL):
            batch_df = df_all_docs.iloc[i:i + RECORDS_PER_LLM_CALL]
            if not batch_df.empty:
                # Создаем задачу для асинхронной обработки каждого пакета
                task = asyncio.create_task(process_one_batch_async(pool, batch_df, semaphore))
                tasks.append(task)

        if tasks:
            await asyncio.gather(*tasks) # Ожидаем завершения всех задач
        else:
            logger.info("Нет пакетов для обработки.")
        # добавляем token_count_global в файл token_count to text
        with open("token_count.txt", "a") as f:
            f.write(str(token_count_global))
        print(token_count_global)

        # # buyer_name -> в верхний регистр
        # await update_buyer_name_upper(pool)
        #
        # # ТТН page_type=1 заполняется из ВН
        # await update_doc_ttn_from_vn(pool)
        #
        # # ВН заполняется из ТТН page_type=1
        # await update_doc_vn_from_ttn(pool)
        #
        # # ТТН page_type=3 или 999 заполняется из ТТН page_type=1
        # await update_doc_ttn_from_ttn(pool)

    except Exception as e:
        # Логирование общей ошибки в main, если что-то пошло не так на верхнем уровне
        logger.error(f"Произошла ошибка в функции main: {e}", exc_info=True)
    finally:
        if pool:
            # logger.info("Закрытие пула соединений с БД.")
            await pool.close()


if __name__ == "__main__":
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")
            
    # print(f"[{datetime.now()}] Запуск основного процесса...") # Отладочный print, можно убрать
    asyncio.run(main())
    # print(f"[{datetime.now()}] Основной процесс завершен.") # Отладочный print, можно убрать