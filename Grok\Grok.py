# pip install GROK python-dotenv
import base64
import os
from openai import OpenAI
from dotenv import load_dotenv
from typing import Dict, Any, Union, List
import json
import asyncio
import sys
from pathlib import Path

parrent_dir = str(Path(__file__).resolve().parent.parent)
sys.path.append(parrent_dir)
from prompt import PROMPT_EXAMPLE_GEMINI


# Загрузка переменных окружения из файла .env
load_dotenv()

# Получаем API ключ из переменной окружения GROK_API_KEY
api_key = os.getenv("GROK_API_KEY")
if not api_key:
    print("Ошибка: Не найден API ключ GROK_API_KEY в файле .env")
    exit(1)

# Создаем клиент GROK с API ключом
client = OpenAI(base_url="https://api.x.ai/v1", api_key=api_key)


def clear_text(json_string: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
    # Пытаемся распарсить JSON, если json_string вернул строку
    if isinstance(json_string, str):
        try:
            json_string = json_string.strip()

            # Обрабатываем различные форматы JSON
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            if json_string.startswith('"'):
                json_string = json_string[1:]
            if json_string.endswith('"'):
                json_string = json_string[:-1]
                
            # Преобразуем строку JSON в словарь Python
            extract_data = json.loads(json_string)
            # print(extract_data) # <-- Оригинальный print, оставляю как есть
            return extract_data
        except json.JSONDecodeError as e:
            print(f"Не удалось распарсить JSON из extract_info: {e}") # <-- Оригинальный print
            return {}
    return json_string if isinstance(json_string, dict) else {}


async def extract_data_by_grok(content: str, prompt = PROMPT_EXAMPLE_GEMINI) -> Dict[str, Any]:
    # Запрос к пользователю для ввода сообщения
    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": content},
    ]

    # Создаем запрос к API
    completion = client.chat.completions.create(
        model="grok-3-mini",  # вход=131072; выход=131072; Ввод=0,30$; Выход=0,50$
        reasoning_effort="high",
        messages=messages,
        temperature=0.1,
        max_completion_tokens=65000,
        top_p=0.1,
        stream=False,
        response_format={"type": "json_object"},
        stop=None,
    )
    response_text = completion.choices[0].message.content
    reasoning_content = completion.choices[0].message.reasoning_content  # рассуждения
    tokens = completion.usage  # все токены
    print(tokens)
    clear_result = clear_text(response_text)
    return clear_result
        

if __name__ == "__main__":
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")
    content = """
Eyeball Marshmallow 4gx100pcsx12boxes'Eyeball Marshmallow 4gx100pcsx12boxes',
Jelly cup in Elephant Jar  13gx100pcsx6jars'Jelly cup in Elephant Jar  13gx100pcsx6jars',
Jelly cup in bear Jar  13gx100pcsx6jars'Jelly cup in bear Jar  13gx100pcsx6jars',
    """
    prompt = """Тебе дан список артикулов. Ты - аналитик, глубоко разбирающийся в ассортименте. 
                Однозначно определяешь где начинается и заканчивается каждый артикул.
                Максимально точно определи и извлеки данные по каждому артикулу в формате ВАЛИДНОГО JSON.                 
                Ты не можешь определить сколько артикулов? Почему извлекаешь данные только для первого артикула?
                Используй только данные из предложенных вариантов.
                {articles:[{
                    "sku": str,  // наименование, которое тебе дал. Без изменений
                    "grams_in_pcs": float,  // grams, ml
                    "pcs_in_block": float,
                    "box_in_cartoon": int,
                    "weight_unit": str, // g, ml, kg, гр, кг, мл
                    "pcs_type": str, // pcs, шт
                    "box_type": str // jar, box, банка, блок
                }]}
            """

    result = asyncio.run(extract_data_by_grok(content, prompt))
    print(result)