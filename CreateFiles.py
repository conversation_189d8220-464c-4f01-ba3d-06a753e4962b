import fitz  # PyMuPDF
import re
import os
from PIL import Image, ImageEnhance, ImageFilter
import pytesseract
import numpy as np

# from deskew import determine_skew # Раскомментировать, если установлены deskew и scikit-image
# from skimage.transform import rotate # Раскомментировать, если установлены deskew и scikit-image
import io
import logging

from ExtractDocTypeDateNumber import extract_doc_date_and_number_vidatkova

# Настройка логирования
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)

# Укажите путь к исполняемому файлу Tesseract, если он не в PATH
# pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'


def preprocess_image(image: Image.Image) -> Image.Image:
    """
    Применяет предобработку к изображению для улучшения качества OCR скан-копий.

    :param image: Объект изображения PIL.
    :return: Обработанный объект изображения PIL.
    """
    try:
        # 1. Преобразование в оттенки серого
        image = image.convert("L")

        # 2. Устранение перекоса (опционально, требует deskew и scikit-image)
        # try:
        #     np_image = np.array(image)
        #     # Определение угла перекоса, ограничение поиска угла
        #     skew_angle = determine_skew(np_image, max_skew=5)
        #     if abs(skew_angle) > 0.1: # Применяем поворот только при заметном угле
        #         logging.info(f"Применение deskew: {skew_angle:.2f} градусов")
        #         # Поворот изображения
        #         # expand=True гарантирует, что изображение не обрежется по углам
        #         image = Image.fromarray((rotate(np_image, skew_angle, resize=True, mode='constant', cval=255) * 255).astype(np.uint8))
        # except ImportError:
        #      logging.warning("Библиотеки 'deskew' и 'scikit-image' не найдены. Устранение перекоса пропущено.")
        # except Exception as e:
        #      logging.warning(f"Ошибка при устранении перекоса: {e}")

        # 3. Повышение контрастности
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.8)  # Значение можно подобрать

        # 4. Повышение резкости
        enhancer = ImageEnhance.Sharpness(image)
        image = enhancer.enhance(1.5)  # Значение можно подобрать

        # 5. Бинаризация (простая адаптивная или по порогу)
        # Адаптивная бинаризация часто лучше для сканов с неравномерным фоном
        # Требует Pillow >= 9.1
        # try:
        #     image = image.filter(ImageFilter.ADAPTIVE_THRESHOLD)
        # except AttributeError:
        # Простая бинаризация по фиксированному порогу, если адаптивная недоступна
        # threshold = 180 # Значение порога можно подобрать
        # image = image.point(lambda x: 0 if x < threshold else 255, '1') # '1' - 1-битное пиксельное изображение
        # logging.warning("Адаптивная бинаризация недоступна (требует Pillow >= 9.1). Использована простая пороговая.")

        # Простая бинаризация по фиксированному порогу
        threshold = 180  # Значение порога можно подобрать (от 0 до 255)
        image = image.point(
            lambda x: 0 if x < threshold else 255, "1"
        )  # '1' - 1-битное пиксельное изображение

    except Exception as e:
        logging.error(f"Ошибка в функции preprocess_image: {e}")
        # В случае ошибки возвращаем исходное изображение или изображение в оттенках серого
        try:
            return image.convert("L")
        except:
            return image  # Возвращаем оригинал, если конвертация тоже не удалась

    return image


def extract_text_from_page(page: fitz.Page) -> str:
    """
    Извлекает текст со страницы PDF с использованием OCR после предобработки.
    Предполагается, что документ является скан-копией.

    :param page: Объект страницы PyMuPDF.
    :return: Текст страницы или пустая строка в случае ошибки.
    """
    try:
        # Проверяем, установлен ли путь к Tesseract
        if not pytesseract.pytesseract.tesseract_cmd or not os.path.exists(
            pytesseract.pytesseract.tesseract_cmd
        ):
            pytesseract.pytesseract.tesseract_cmd = (
                r"C:\Program Files\Tesseract-OCR\tesseract.exe"
            )

        pix = page.get_pixmap(dpi=600)
        img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
        processed_img = preprocess_image(img)

        whitelist = "0123456789,-№ІіЇїҐґЄєабвгґдеєжзиіїйклмнопрстуфхцчшщьюяАБВГҐДЕЄЖЗИІЇЙКЛМНОПРСТУФХЦЧШЩЬЮЯ"
        tesseract_config = "--psm 6 " f"-c tessedit_char_whitelist={whitelist}"

        # Пробуем с разными языковыми настройками
        try:
            text = pytesseract.image_to_string(
                processed_img, lang="ukr+rus+eng", config=tesseract_config
            )
        except Exception as lang_error:
            print(
                f"Ошибка с языковыми файлами: {lang_error}. Пробуем только с английским..."
            )
            text = pytesseract.image_to_string(
                processed_img, lang="eng", config=tesseract_config
            )

        text = re.sub(r"[^\S\n]+", " ", text)
        text = re.sub(r"\n\s*\n", "\n", text)

        return text

    except pytesseract.TesseractNotFoundError:
        logging.error(
            "Tesseract не найден. Убедитесь, что он установлен и путь к нему указан."
        )
        print(
            "Ошибка: Tesseract не найден. Проверьте установку и путь к исполняемому файлу."
        )
        return ""
    except Exception as e:
        logging.error(
            f"Ошибка OCR или обработки изображения на странице {page.number + 1}: {e}"
        )
        return ""


def sanitize_filename(filename: str) -> str:
    """
    Очищает строку для использования в качестве имени файла, заменяя недопустимые символы.

    :param filename: Исходная строка.
    :return: Очищенная строка.
    """
    # Заменяем пробелы на подчеркивания
    filename = filename.replace("  ", " ")
    # Удаляем недопустимые символы для Windows/Linux
    illegal_chars = r'[\\/:\*\?"<>|\'\s]'  # Добавлен пробел и апостроф, т.к. пробелы уже заменили, но на всякий случай
    filename = re.sub(illegal_chars, "", filename)
    # Удаляем точки в начале или конце имени файла (кроме точки перед расширением)
    filename = filename.strip(" .")
    # Убедимся, что не получилось пустое имя
    if not filename:
        filename = "unnamed_document"
    return filename


def save_document_optimized(
    input_doc: fitz.Document, doc_info: dict, page_numbers: list[int], output_dir: str
):
    """
    Сохраняет документ в новый PDF-файл с оптимизацией размера путем
    рендеринга страниц с более низким DPI и сжатия изображений.

    :param input_doc: Входной PDF-документ.
    :param doc_info: Информация о документе (тип, номер, дата).
    :param page_numbers: Список номеров страниц, принадлежащих документу.
    :param output_dir: Папка для сохранения.
    """
    if not page_numbers:
        logging.warning("Попытка сохранить пустой документ (нет страниц). Пропущено.")
        return

    # Формирование имени файла
    if doc_info.get("doc_date"):
        # Формат: тип_документа_номер_документа_дата.pdf
        base_filename = (
            f"{doc_info['doc_type']}_{doc_info['doc_number']}_{doc_info['doc_date']}"
        )
    else:
        # Если дата не найдена
        base_filename = f"{doc_info['doc_type']}_{doc_info['doc_number']}_без_даты"  # Добавляем пометку
    base_filename = sanitize_filename(base_filename)

    filename = f"{base_filename}.pdf"
    output_path = os.path.join(output_dir, filename)

    logging.info(
        f"Попытка сохранить документ '{base_filename}' ({len(page_numbers)} стр.) в {output_path}"
    )

    output_doc = fitz.open()
    # Настройки для оптимизации размера
    # Более низкий DPI уменьшает разрешение изображения
    # Качество JPEG (от 0 до 95+, 95 - лучшее, но больший размер)
    target_dpi = 120  # Можно попробовать 100, 150 или 200
    jpeg_quality = 85  # Можно попробовать от 70 до 90

    logging.info(
        f"Оптимизация: рендеринг страниц с DPI={target_dpi}, качество JPEG={jpeg_quality}"
    )

    try:
        for i, page_num in enumerate(page_numbers):
            if page_num < 0 or page_num >= input_doc.page_count:
                logging.warning(f"Неверный номер страницы: {page_num}. Пропущено.")
                continue

            page = input_doc.load_page(page_num)

            # Рендерим страницу в pixmap с целевым DPI
            pix = page.get_pixmap(dpi=target_dpi)

            # Конвертируем в PIL Image
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

            # Сохраняем изображение в буфер в формате JPEG с заданным качеством
            img_buffer = io.BytesIO()
            img.save(img_buffer, format="JPEG", quality=jpeg_quality)
            img_buffer.seek(0)  # Перематываем буфер в начало

            # Создаем временный одностраничный PDF из этого изображения
            temp_pdf = fitz.open("pdf", img_buffer.read())

            # Добавляем страницу из временного PDF в наш выходной документ
            # insert_pdf(from_page, to_page)
            output_doc.insert_pdf(
                temp_pdf
            )  # Просто добавляем все страницы из temp_pdf (там только одна)

            temp_pdf.close()  # Закрываем временный документ

        # Сохраняем выходной документ с дополнительной очисткой и сжатием объектов
        # Это помогает уменьшить размер PDF-объектов, но основное уменьшение
        # достигается за счет сжатия изображений на предыдущем шаге.
        output_doc.save(output_path, garbage=4, deflate=True, clean=True)
        output_doc.close()

        logging.info(f"Документ сохранен: {output_path}")

        # Проверка размера файла
        try:
            file_size_kb = os.path.getsize(output_path) / 1024
            logging.info(f"Размер файла: {file_size_kb:.2f} KB.")
            if file_size_kb > 500:
                logging.warning(
                    f"Файл '{filename}' ПРЕВЫСИЛ целевой размер 500 KB ({file_size_kb:.2f} KB). Возможно, потребуется дальнейшее снижение качества или итеративный подход."
                )
        except Exception as size_check_error:
            logging.warning(f"Не удалось проверить размер файла: {size_check_error}")

    except Exception as e:
        logging.error(f"Ошибка при сохранении документа '{base_filename}': {e}")
        # Попытка закрыть output_doc в случае ошибки
        try:
            output_doc.close()
        except:
            pass


def extract_documents(input_path: str, output_dir: str):
    """
    Извлекает каждый документ из PDF в отдельный файл на основе OCR.

    :param input_path: Путь к входному PDF-файлу (скан-копия).
    :param output_dir: Папка для сохранения выходных файлов.
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        logging.info(f"Создана папка для сохранения: {output_dir}")

    try:
        doc = fitz.open(input_path)
        logging.info(f"Открыт документ: {input_path} ({doc.page_count} страниц)")
    except Exception as e:
        logging.error(f"Не удалось открыть PDF файл {input_path}: {e}")
        return

    current_doc_pages: list[int] = []
    current_doc_info: dict | None = None
    processed_docs_count = 0

    for page_num in range(len(doc)):
        logging.info(f"Анализ страницы {page_num + 1}/{doc.page_count}...")
        page = doc.load_page(page_num)

        # Извлекаем текст со страницы (с OCR и предобработкой)
        text = extract_text_from_page(page)

        # Пытаемся найти информацию о документе (тип, номер, дата)
        # data = extract_doc_info_from_text(text)
        data = extract_doc_date_and_number_vidatkova(text)

        if data:
            # Найдена информация о документе - это потенциально новая граница документа

            if current_doc_info:
                # Если у нас уже накапливались страницы для предыдущего документа, сохраняем его
                logging.info(
                    f"На странице {page_num + 1} найдена новая информация о документе. Сохраняем предыдущий документ."
                )
                save_document_optimized(
                    doc, current_doc_info, current_doc_pages, output_dir
                )
                processed_docs_count += 1
            else:
                # Это информация о первом документе в файле
                logging.info(
                    f"На странице {page_num + 1} найдена информация о первом документе."
                )

            # Начинаем накапливать страницы для нового документа
            current_doc_info = data
            current_doc_pages = [page_num]

        else:
            # Информация о документе не найдена на этой странице
            if current_doc_info:
                # Если у нас уже начат документ, добавляем эту страницу к нему
                logging.debug(
                    f"Страница {page_num + 1} добавлена к текущему документу: {current_doc_info.get('doc_type', 'N/A')} №{current_doc_info.get('doc_number', 'N/A')}"
                )
                current_doc_pages.append(page_num)
            else:
                # Информация о документе не найдена, и документ еще не начат.
                # Это может быть вводная страница или ошибка. Пока игнорируем такие одиночные страницы без заголовка.
                logging.warning(
                    f"На странице {page_num + 1} не найдена информация о документе. Страница игнорируется, т.к. предыдущий документ не определен."
                )
                pass  # Или можно добавить логику для обработки таких страниц

    # После цикла обязательно сохраняем последний накопленный документ
    if current_doc_info:
        logging.info("Конец документа. Сохраняем последний документ.")
        save_document_optimized(doc, current_doc_info, current_doc_pages, output_dir)
        processed_docs_count += 1
    else:
        logging.warning(
            "Обработка завершена. Документная информация не найдена ни на одной странице."
        )

    doc.close()
    logging.info(f"Обработка завершена. Сохранено {processed_docs_count} документов.")


if __name__ == "__main__":
    # Укажите путь к вашему исходному PDF файлу
    input_pdf_path = r"c:\Users\<USER>\Desktop\Сверка\Scan\ВЕРЕСЕНЬ 2024\ScanForTreaningNew_fixed.pdf"

    # Папка для сохранения будет там же, где исходный файл
    output_directory = os.path.dirname(input_pdf_path)
    # Или можно указать другую папку:
    # output_directory = r"d:\Processed_Documents"

    # Проверка существования исходного файла
    if not os.path.exists(input_pdf_path):
        logging.error(f"Исходный файл не найден: {input_pdf_path}")
    else:
        extract_documents(input_pdf_path, output_directory)
