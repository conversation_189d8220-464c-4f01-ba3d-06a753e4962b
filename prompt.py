# Low Temperature (0.1) + Low Top-p (0.2) = Максимальная точность
# Low Temperature (0.1) + High Top-p (0.9) = Точность с небольшим разнообразием  
# High Temperature (0.8) + Low Top-p (0.2) = Контролируемая креативность
# High Temperature (0.8) + High Top-p (0.9) = Максимальное разнообразие

PROMPT_CLEAR_TEXT = """
Тебе дается OCR текст на украинском языке.
Ты - финансовый аналитик с многолетним опытом. Ты умеешь однозначно и точно определять по контексту с каким документом работаешь.
Проверь текст документа и найди в нем аномалии, не корректности, ошибки, незначащие повторы, несогласованности и т.д.
Проверь, чтобы конец текста был пустым или повторяющимися словами, символами.
Глубоко продумай каждую деталь и верни исправленный вариант текста, соблюдая формат и разметки.
** !!!БЕЗ КОММЕНТАРИЕВ, БЕЗ РАССУЖДЕНИЙ, БЕЗ ФАНТИЗАРОВАНИЯ, БЕЗ ПЕРЕВОДА И БЕЗ ДОПОЛНЕНИЯ!!! **
**ЯЗЫК НЕ МЕНЯТЬ!!!**
"""

PROMPT_AMOUNT_WITH_VAT = """
Тебе дается OCR текст на украинском языке.
Ты - финансовый аналитик с многолетним опытом. Ты умеешь однозначно и точно определять по контексту с каким документом работаешь.
Ты глубоко и детально разобрался во всех цифрах, числах, суммах данных тебе. И разобрался где суммы с НДС, где БЕЗ НДС, где количество. 
Где суммы написаны числом, а где прописью. Ты их глубоко проанализировал, многократно и детально перепроверил!!!.
Выведи мне сумму с НДС числом, в формате словаря: {'amount_with_vat':number}. 
Если нет суммы с НДС или ты не можешь его вычислить верни: {'amount_with_vat':0}.
** !!!БЕЗ КОММЕНТАРИЕВ, БЕЗ РАССУЖДЕНИЙ, БЕЗ ФАНТИЗАРОВАНИЯ, БЕЗ ПЕРЕВОДА И БЕЗ ДОПОЛНЕНИЯ!!! **
"""

PROMPT_OCR = """
  **Начало Промта для ИИ**

  **Задание:** Обработка отсканированного PDF-документа (язык: украинский).

  **Контекст:** Тебе предоставлен отсканированный PDF-файл на украинском языке. Качество сканирования может быть крайне низким: страницы могут быть перевернуты, содержать многочисленные артефакты, быть плохо выровненными, иметь значительный шум, искажения, "мусор" или очень размытый текст.

  **Твоя основная задача:** Максимально точно и полно извлечь ВЕСЬ **осмысленный и читаемый** текстовый контент из каждой страницы документа. При этом глубоко проанализировать его визуальную и логическую структуру для точного и форматированного воспроизведения в Markdown.

  **Ключевые требования к выполнению:**

  1.  **Полнота извлечения читаемого текста:**
      *   Извлеки абсолютно весь **осмысленный и читаемый** текст с каждой непустой страницы.
      *   Не допускай пропусков **осмысленных** символов, слов, строк или целых блоков текста, которые можно однозначно распознать.
      *   Если страница не содержит никакого текста или значимых графических элементов (полностью пустая), выведи для такой страницы строго одну строку: `EMPTY_PAGE`.

  2.  **Точность и достоверность распознавания (с акцентом на осмысленность):**
      *   Обеспечь наивысшую возможную точность распознавания **осмысленных символов и слов** украинского алфавита.
      *   **Категорически запрещено:**
          *   Вставлять любые символы, которых нет в исходном документе.
          *   Генерировать или транскрибировать длинные, бессмысленные последовательности символов, которые являются результатом шума, артефактов сканирования или ошибочного распознавания "мусора" как текста.
          *   Если определенный фрагмент текста или символ абсолютно нечитаем, или распознается как длинная, нечитаемая, нелогичная или бессмысленная последовательность символов, приоритетом является **не включение этих бессмысленных данных** в вывод. Вместо этого, стремись к точному воспроизведению только осмысленного и читаемого контента.
          *   В случае, если содержимое ячейки таблицы или целого блока текста состоит исключительно из нечитаемого шума, эта часть содержимого должна быть опущена (т.е. ячейка или блок должны быть пустыми), а не заполнены случайными символами или длинными строками "мусора".

  3.  **Максимальное сохранение оригинального форматирования и структуры:**
      *   **Порядок элементов:** Слова, строки, абзацы и любые другие текстовые блоки должны следовать в том же порядке, что и в оригинальном документе. Не меняй их местами.
      *   **Переносы строк:** Точно воспроизводи оригинальные переносы строк. Не объединяй строки и не создавай новые искусственно.
      *   **Абзацы:** Сохраняй структуру абзацев, включая визуально определяемые отступы или пустые строки между ними.
      *   **Пробелы:** Воспроизводи пробелы между словами и символами так, как они представлены в оригинале.
      *   **Таблицы:** Если на странице присутствуют таблицы, ты **ОБЯЗАН** распознать их структуру (строки, столбцы, ячейки) и **корректно отформатировать** их с использованием Markdown-синтаксиса для таблиц. Содержимое ячеек должно быть точно передано, следуя правилам пункта 2.
      *   **Визуальные атрибуты текста (по возможности):** Если возможно надежно определить и воспроизвести в Markdown такие атрибуты, как **жирный шрифт**, *курсив* или `моноширинный текст`, сохрани их. Однако абсолютным приоритетом является точность извлечения осмысленного текста и сохранение общей структуры.

  4.  **Глубокий анализ текста (как основа для воспроизведения):**
      *   Твой "глубокий анализ" подразумевает тщательное понимание визуальной и логической структуры документа. Это включает анализ расположения текстовых блоков, выравнивания, наличия списков, заголовков, подзаголовков, колонок, а также шрифтовых особенностей (если они несут структурное значение), чтобы максимально точно воспроизвести их в Markdown.

  5.  **Формат вывода:**
      *   Весь извлеченный тобой контент для КАЖДОЙ СТРАНИЦЫ (или всего документа, если он короткий) должен быть представлен СТРОГО в виде единого блока Markdown.
      *   Твой ответ ДОЛЖЕН начинаться с ````markdown` и заканчиваться на ````. Никаких других символов, текста, объяснений или комментариев до или после этого блока быть не должно.
      *   **Таблицы внутри Markdown:** Убедись, что таблицы отформатированы правильно с использованием синтаксиса Markdown (заголовки, разделители, ячейки).

  6.  **Самопроверка перед выводом:**
      *   **Проверь, что твой вывод начинается с "```markdown" и заканчивается на "```". Если нет, исправь это.**
      *   **Убедись, что текст данного промта (этих инструкций) НЕ ПОЯВЛЯЕТСЯ в твоем итоговом ответе.** Твой ответ должен содержать только извлеченный из PDF текст (или `EMPTY_PAGE`) в указанном Markdown формате.
      *   Перепроверь, что ты не пропустил **осмысленный** текст, не добавил лишних (бессмысленных) символов и максимально сохранил оригинальный формат.

  **Язык документа:** Украинский. Все алгоритмы распознавания и анализа должны быть настроены на украинский язык.
  
  **ВАЖНО:**
    Проверь строки. Убери все последовательно идущие повторяющиеся символы, Которые НЕ несут смысловую нагрузку.
    Данные должны быть строго из данного тебе документа.
    Добавлять от себя, додумывать, фантазировать, думать что опечатка - ЗАПРЕЩЕНО!!!.

  **Приступай к выполнению.**
"""

GEMINI_AI_THINKS = """**Analyzing Accounting Data**

I've been working on extracting the critical data elements from the Ukrainian accounting documents, specifically focusing on identifying document types, dates, and numbers. I've encountered some hurdles in accurately capturing the referenced invoice numbers, particularly the TT numbers. I'm refining the pattern recognition to improve accuracy and ensure I capture all relevant information.


**Identifying Key Data Elements**

I'm currently focused on extracting the core data points from the Ukrainian accounting documents. This involves identifying the document type, date, and number, along with the buyer information. I'm building out the logic to handle multi-page documents, accurately determining their sequence. I'm also working to precisely pinpoint referenced invoice numbers, especially for TTN documents. I'm aiming for accuracy in extracting total amounts with VAT.


**Refining Document Boundaries**

I'm currently focused on precisely identifying the boundaries of each Ukrainian accounting document within the PDF. I'm leveraging keywords and page layout analysis to distinguish between \"Видаткова накладна\" and \"Товарно-транспортна накладна.\"  I've incorporated logic to handle multi-page documents, ensuring correct page sequencing.  I'm also working to extract the associated dates and buyer information.


**Structuring the Extraction Logic**

I'm currently focused on organizing the extraction logic.  I've built a framework to identify document types and crucial information.  I'm refining the process for handling multi-page documents like \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" My approach now involves accurately capturing dates, buyer details, and referenced invoice numbers for each document type, alongside total amounts with VAT.


**Defining Extraction Strategies**

I've been working on crafting precise extraction strategies for the Ukrainian accounting documents. I'm focusing on parsing documents to detect keywords, document types, date, number, and buyer details, including EDRPOU codes. I've also incorporated logic to handle multi-page documents, including both \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" My plans incorporate the identification of referenced invoice numbers and the calculation of VAT. I'm carefully constructing the logic to output the data in the requested JSON format. I also understand how the user wants to treat TTN and VN documents separately.


**Formulating Extraction Logic**

I've crafted an initial strategy to extract the required data, including document types, dates, and buyer details, with EDRPOU codes and referenced invoice numbers for TTNs. I've begun to tackle handling multi-page documents, specifically the \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" My plans incorporate identifying dates, buyer details, and referenced invoice numbers for each document type, alongside total amounts with VAT. I understand how to format the data into the specified JSON output.


**Implementing PDF Data Extraction**

I'm currently focused on implementing the core logic for extracting the required data. I'm prioritizing the identification of document boundaries, buyer information, and document types. I'm meticulously parsing page content to capture dates, numbers, and referenced invoice numbers for each document. The planned output is JSON format, and I've started adding the calculated differences for TTNs. I'm using OCR results for pages 1-6 as input data.


**Developing Data Extraction Logic**

I'm currently focused on extracting information from the provided OCR data, particularly for the two documents across pages 1-6. I've designed logic to handle both \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" My approach now includes extracting buyer details, dates, and referenced invoice numbers for each document. I'm also planning to calculate and output the amount difference for TTNs, and I understand the user's need to separate VN and TTN documents.


**Developing Data Structures**

I've constructed data structures to hold the extracted information. I'm focusing on defining how to represent different document types (VN and TTN), including their associated details like dates, buyer info, and referenced invoices. I'm also preparing for the calculation of the amount difference, and I am adding fields for page numbers.


**Developing Extraction Algorithms**

I've formulated a preliminary extraction strategy and identified key data:  types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output.  I've noted the difference between the VN and TTN documents.


**Developing Robust Data Structures**

I'm currently focused on constructing detailed data structures to hold the extracted information, considering multi-page documents and referenced invoices. I've designed structures for \"Видаткова накладна\" (VN) and \"Товарно-транспортна накладна\" (TTN), including their respective dates, numbers, buyers, and page numbers. I am adding a method to calculate the amount difference for TTNs based on the referenced VNs and am making plans for the JSON format.


**Refining Data Extraction Strategies**

I'm focused on meticulously extracting all the details from the provided OCR data for those Ukrainian accounting documents. I've designed logic to handle both \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" My approach now incorporates logic to extract buyer information, dates, and referenced invoice numbers for each document type and page number, along with the total amount with VAT. I'm also preparing to calculate and output the amount difference for TTNs based on the referenced VNs in JSON format.


**Implementing Data Structures**

I've established data structures to hold extracted information. For \"Видаткова накладна\" (VN) and \"Товарно-транспортна накладна\" (TTN), I've designed structures for their respective details, dates, numbers, buyers, page numbers, and referenced invoices for TTNs. I'm incorporating the calculation of the amount difference. I'm planning the output in JSON format.


**Optimizing Data Extraction**

I'm focused on developing the extraction logic, now incorporating handling multi-page \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" I've added a difference calculation for TTNs based on referenced VNs.  I am building on the understanding of extracting buyer information, dates, numbers, and page numbers. I've designed logic to provide the data in the requested JSON format, following the user's specific request.


**Developing Extraction Logic**

I've formulated a preliminary extraction strategy and identified key data: types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output. I've noted the difference between the VN and TTN documents, including specific page numbers for each. I'm also planning to calculate and output the amount difference for TTNs based on their corresponding VNs. I've created plans for how to format the data into the requested JSON output. I've also identified the user's specific requirement.


**Developing Output Formatting**

I've been working on extracting the required data, now meticulously adding page numbers to the JSON output. I've designed logic to handle both \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" My plans incorporate identifying dates, buyer details, and referenced invoice numbers for each document type, alongside total amounts with VAT. I'm carefully constructing the logic to output the data in the requested JSON format, with each page number listed.


**Developing Data Extraction**

I've formulated a preliminary strategy for extracting the required data, now incorporating multi-page support. I'm focusing on \"Видаткова накладна\" and \"Товарно-транспортна накладна.\"  My plans incorporate the extraction of dates, buyer details, page numbers, and referenced invoice numbers for each document type, alongside total amounts with VAT.  I am building on the difference calculation for TTNs, and formatting to the requested JSON. I am developing plans for each page number.


**Developing Extraction Algorithms**

I've formulated a preliminary extraction strategy and identified key data: document types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output.  I've noted the difference between the VN and TTN documents. I am now in the process of building logic to identify and handle pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I've corrected the error of row numbering and am also now accounting for TTNs.


**Developing Extraction Logic**

I've formulated a preliminary extraction strategy and identified key data: document types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output.  I've noted the difference between the VN and TTN documents, including specific page numbers for each. I'm also planning to calculate and output the amount difference for TTNs based on their corresponding VNs. I've created plans for how to format the data into the requested JSON output. I've also identified the user's specific requirement.


**Refining Data Extraction**

I've made considerable progress on parsing the provided OCR data, particularly focusing on pages 1-6. I've successfully implemented extraction logic for both \"Видаткова накладна\" and \"Товарно-транспортна накладна,\" including date, number, buyer details (name and EDRPOU code), and referenced invoice numbers for TTNs. I've also added the total amounts with VAT to the JSON. I've corrected the row numbers, and I've also added support for each page. Now, I'm working to calculate the amount difference for TTNs based on the referenced VNs in JSON format, in pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are now 1, 2, and 4.


**Extracting and Processing Data**

I'm focused on extracting the necessary data. I've designed the extraction strategy to capture data on each page, including extracting document type, date, number, buyer details (name and EDRPOU code), and referenced invoice numbers for TTNs. I have corrected the row numbers. Also, the plan incorporates the total amount with VAT. For pages 1, 2, 3, 4, 5, and 6, I am preparing the data for the calculation of the amount difference for TTNs based on the referenced VNs in JSON format. The correct row numbers for VN 8825, page 4, are 1, 2, and 4. I am in the final stages of JSON output formatting.


**Extracting Data and Formatting**

I'm focused on extracting the necessary data. I've designed the extraction strategy to capture data on each page, including document type, date, number, buyer details (name and EDRPOU code), and referenced invoice numbers for TTNs. I have corrected the row numbers and added support for each page. Now, I'm working to calculate the amount difference for TTNs based on the referenced VNs in JSON format, in pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are now 1, 2, and 4. I am close to finishing the JSON formatting.


**Extracting and Processing Data**

I'm focused on extracting the necessary data. I've designed the extraction strategy to capture data on each page, including document type, date, number, buyer details (name and EDRPOU code), and referenced invoice numbers for TTNs. I have corrected the row numbers and added support for each page. Now, I'm working to calculate the amount difference for TTNs based on the referenced VNs in JSON format, in pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are now 1, 2, and 4. I am close to the final JSON formatting.


**Implementing Final JSON Generation**

I'm now fully immersed in developing the JSON generation logic. I've refined the approach to extract data, incorporating date formatting and handling edge cases. I've built the extraction logic, now with page numbers and row numbers for each entry, for each page, including page 4 with the specific rows: 1, 2, 4. I've added a difference calculation for TTNs based on the referenced VNs. Finally, I'm working to produce the formatted JSON output, specifically for the specified pages. I can handle both VN and TTN document types and calculate differences.


**Refining JSON Output**

I'm now fully immersed in developing the JSON generation logic. I've refined the approach to extract data, incorporating date formatting and handling edge cases. I've built the extraction logic, now with page numbers and row numbers for each entry, for each page, including page 4 with the specific rows: 1, 2, 4. I've added a difference calculation for TTNs based on the referenced VNs. Finally, I'm working to produce the formatted JSON output, specifically for the specified pages. I can handle both VN and TTN document types and calculate differences.


**Implementing JSON Generation**

I'm focused on meticulously extracting all the details from the provided OCR data. I am creating the logic to generate JSON for the pages, handling document types. I will correct the row numbers and add support for each page. Now, I'm working to calculate the amount difference for TTNs based on the referenced VNs in JSON format, in pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are now 1, 2, and 4. I am nearing completion.


**Generating Final JSON Output**

I'm now fully immersed in the final stage: JSON generation and refinement for all pages and types. I've designed the output logic with the necessary calculations, particularly the difference for TTNs based on referenced VNs. I am checking the results and readying for JSON output. All row and page numbers are now correct, as per requirements. I'm checking the data.


I've made progress on parsing the OCR data for all pages. I'm incorporating the date, number, buyer details (name and EDRPOU), document type, referenced invoice numbers for TTNs, and also total amount with VAT into the JSON output. I'm refining the JSON generation with the requested structure, and now the correct row numbers are 1, 2, and 4 for VN 8825, page 4. I will create a final output.


**Implementing JSON Output**

I'm now focused on delivering the fully formatted JSON output, incorporating the date, number, buyer details (name and EDRPOU), document type, referenced invoice numbers for TTNs, and total amounts with VAT. I have corrected the row and page numbers. I've finished the JSON structure for pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I am ready to generate the JSON.


**Finalizing Data Extraction**

I've completed the extraction logic and built the JSON output structure. I've accounted for each document, including page and row numbers.  I've incorporated difference calculations and formatting, delivering accurate data in the requested JSON format. I am completing the final check.


**Implementing JSON Generation**

I'm now focused on delivering the fully formatted JSON output, incorporating the date, number, buyer details (name and EDRPOU), document type, referenced invoice numbers for TTNs, and total amounts with VAT. I have corrected the row and page numbers. I've finished the JSON structure for pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I am ready to generate the JSON.


**Generating JSON Output**

I've formulated a preliminary extraction strategy and identified key data: types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output. I've noted the difference between the VN and TTN documents. I am now in the process of building logic to identify and handle pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I've corrected the error of row numbering and am also now accounting for TTNs.


**Extracting and Processing**

I'm now fully immersed in developing the JSON generation logic. I've refined the approach to extract data, incorporating date formatting and handling edge cases. I've built the extraction logic, now with page numbers and row numbers for each entry, for each page, including page 4 with the specific rows: 1, 2, 4. I've added a difference calculation for TTNs based on the referenced VNs. Finally, I'm working to produce the formatted JSON output, specifically for the specified pages. I can handle both VN and TTN document types and calculate differences.


I've been working on extracting the critical data. I'm focused on creating the extraction logic to handle various Ukrainian accounting document types, including multi-page ones. Now I'm adding a mechanism for the difference calculation. I'm focusing on parsing the OCR data for specific pages: 1, 2, 3, 4, 5, and 6. For the \"Видаткова накладна\" on page 4, I've confirmed that the correct row numbers are 1, 2, and 4. I will create a final output.


**Refining JSON Output**

I've finalized my extraction strategy, ensuring I handle multi-page documents like \"Видаткова накладна\" and \"Товарно-транспортна накладна\" with date and number correction. I've designed the JSON structure for specific pages. I'm building logic to add a difference calculation for TTNs based on the referenced VNs.  The output is nearly ready.

**Extracting Data and Formatting**

I'm focused on extracting the necessary data. I've designed the extraction strategy to capture data on each page, including document type, date, number, buyer details (name and EDRPOU code), and referenced invoice numbers for TTNs. I have corrected the row numbers and added support for each page. Now, I'm working to calculate the amount difference for TTNs based on the referenced VNs in JSON format, in pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are now 1, 2, and 4. I am nearing completion.


I've completed the extraction logic and built the JSON structure, including handling multi-page \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" I'm also preparing for the calculation of the amount difference.  I've added support for all pages and corrected the row numbers. The system outputs correct data.


**Implementing JSON Generation**

I'm now generating the JSON output, including date, buyer information, and document details, plus the page number. I'm focused on the correct output. I'm adding a difference calculation for the TTNs based on the referenced VNs and working with row numbers.


**Generating Final JSON**

I've formulated a preliminary extraction strategy and identified key data: document types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output.  I've noted the difference between the VN and TTN documents. I am now in the process of building logic to identify and handle pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I've corrected the error of row numbering and am also now accounting for TTNs.


**Generating JSON Output**

I've formulated a preliminary extraction strategy and identified key data: document types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output.  I've noted the difference between the VN and TTN documents. I am now in the process of building logic to identify and handle pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I've corrected the error of row numbering and am also now accounting for TTNs.


**Refining JSON Output**

I've carefully structured the JSON output, including date, buyer information, document details, and page numbers. I've designed the output logic with the necessary calculations. I will create the JSON output with all pages and types. The output is ready.

**Generating JSON Output**

I'm now fully immersed in developing the JSON generation logic. I've refined the approach to extract data, incorporating date formatting and handling edge cases. I've built the extraction logic, now with page numbers and row numbers for each entry, for each page, including page 4 with the specific rows: 1, 2, 4. I've added a difference calculation for TTNs based on the referenced VNs. Finally, I'm working to produce the formatted JSON output, specifically for the specified pages. I can handle both VN and TTN document types and calculate differences.


I've successfully finished extracting and formatting the data. I've designed the output logic with the necessary calculations, particularly the difference for TTNs based on referenced VNs, page and row numbers. The JSON output, which I'll show next, is ready.

**Implementing JSON Generation**

I'm now focused on delivering the fully formatted JSON output, incorporating the date, number, buyer details (name and EDRPOU), document type, referenced invoice numbers for TTNs, and total amounts with VAT. I have corrected the row and page numbers. I've finished the JSON structure for pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I am ready to generate the JSON.


**Extracting and Processing**

I've formulated a preliminary extraction strategy and identified key data: document types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output.  I've noted the difference between the VN and TTN documents. I am now in the process of building logic to identify and handle pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I've corrected the error of row numbering and am also now accounting for TTNs.

**Implementing Final Output**

I've formulated a preliminary extraction strategy and identified key data: types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output.  I've noted the difference between the VN and TTN documents, including specific page numbers for each. I'm also planning to calculate and output the amount difference for TTNs based on their corresponding VNs. I've created plans for how to format the data into the requested JSON output. I've also identified the user's specific requirement.


I'm now focusing on extracting information for the given OCR data. I've designed the output logic with the necessary calculations, particularly the difference for TTNs based on referenced VNs, page and row numbers, plus date and amount formatting. The JSON output, which I'll show next, is ready.


**Implementing JSON Generation**

I'm now generating the JSON output, including date, buyer information, and document details, plus the page number. I'm focused on the correct output. I'm adding a difference calculation for the TTNs based on the referenced VNs and working with row numbers.


I'm working on extracting all the requested information from the provided OCR data, carefully checking and verifying the output, and correcting any inconsistencies or mistakes. I'm building a logic for the extraction to match each entry to its relevant page. I'm focusing on \"Видаткова накладна\" (VN) and \"Товарно-транспортна накладна\" (TTN), in the specified pages. I'm planning to produce an accurately formatted JSON output.


**Implementing JSON Output**

I'm now delivering the fully formatted JSON output, incorporating date, buyer information, document details, and page numbers. I've designed the output logic with the necessary calculations. The system is completing the generation for all pages.


**Final JSON Output Implementation**

I'm now fully immersed in developing the JSON generation logic. I've refined the approach to extract data, incorporating date formatting and handling edge cases. I've built the extraction logic, now with page numbers and row numbers for each entry, for each page, including page 4 with the specific rows: 1, 2, 4. I've added a difference calculation for TTNs based on the referenced VNs. Finally, I'm working to produce the formatted JSON output, specifically for the specified pages. I can handle both VN and TTN document types and calculate differences.


**Finalizing JSON Output**

I've crafted a comprehensive extraction strategy, now generating JSON with page numbers and row numbers for accurate data. I'm focusing on \"Видаткова накладна\" and \"Товарно-транспортна накладна.\"  I've added the difference calculation for TTNs based on the referenced VNs.  The output is almost done.


**Refining JSON Output**

I'm finalizing the JSON generation for pages 1-6, including date, number, buyer details (name and EDRPOU code), document type, referenced invoice numbers for TTNs, and also total amount with VAT. I have corrected the row and page numbers. I've built the final structure.

**Generating Final Output**

I'm now fully immersed in developing the JSON generation logic. I've refined the approach to extract data, incorporating date formatting and handling edge cases. I've built the extraction logic, now with page numbers and row numbers for each entry, for each page, including page 4 with the specific rows: 1, 2, 4. I've added a difference calculation for TTNs based on the referenced VNs. Finally, I'm working to produce the formatted JSON output, specifically for the specified pages. I can handle both VN and TTN document types and calculate differences.


**Refining JSON Output**

I'm now fully immersed in developing the JSON generation logic. I've refined the approach to extract data, incorporating date formatting and handling edge cases. I've built the extraction logic, now with page numbers and row numbers for each entry, for each page, including page 4 with the specific rows: 1, 2, 4. I've added a difference calculation for TTNs based on the referenced VNs. Finally, I'm working to produce the formatted JSON output, specifically for the specified pages. I can handle both VN and TTN document types and calculate differences.


**Implementing JSON Generation**

I'm now generating the JSON output, including date, buyer information, and document details, plus the page number. I'm focused on the correct output. I'm adding a difference calculation for the TTNs based on the referenced VNs and working with row numbers.


**Refining Data Extraction**

I'm focused on developing the extraction logic, now incorporating handling multi-page \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" I've added a difference calculation for TTNs based on referenced VNs.  I am building on the understanding of extracting buyer information, dates, numbers, and page numbers. I've designed logic to provide the data in the requested JSON format, following the user's specific request.


**Generating JSON Output**

I've formulated a preliminary extraction strategy and identified key data: document types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output.  I've noted the difference between the VN and TTN documents. I am now in the process of building logic to identify and handle pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I've corrected the error of row numbering and am also now accounting for TTNs.

"""

GEMINI_AI_PROMPT = """
Ты - высококвалифицированный специалист по анализу и обработке финансовых, бухгалтерских документов. 
Ты обладаешь навыками извлечения ключевой информации из текста на украинском языке.
По контексту ты можешь определить тип документа. Стороны -лицевая, оборотная, внутренняя.
Ты можешь извлекать дату, номер, наименование клиента, код клиента, номера документов, страницы, на которых они расположены.
Ты четко различаешь суммы с НДС и без НДС.
Ты четко различаешь номера документов, на которые ссылается Товарно-транспортная накладная.
Ты четко различаешь даты. Если дата не указана, то ты знаешь, что можно посмотреть на дату документа, на который ссылается ТТН и извлекаешь ее.
Твоя задача извлечь:
1) тип документа. "Товарно-Транспортная" (ТТН), "Видаткова накладна" (ВН), Акт, "Повернення посточальнику" (ПП), Не смог определить - "Другой".
2) Наименование клиента/покупателя. Без Правового статуса. Имя клиента полностью, большими буквами. Данные продавца игнорируй.
3) код покупателя // для уникальности клиента. Если у ВН нет, тогда найди док ТТН, который ссылается на этот ВН и возьми код клиента из ТТН. Если нет, тогда оставь пустым.
4) страницы, которые относятся к данному документу
5) определи где первая страница, где середина и где последняя страница
6) страницы у тебя повторяться не должны. Одна страница относится только к одному документу.
7) номера документов, на которые ссылается ТТН.
8) Если у ТТН нет даты и она ссылается на несколько ВН с разными датами, бери самую позднюю или если все даты совпадают бери любую.
9) Укажи сумму с НДС. Для ВН, она на последней странице(если много страниц). Для ТТН сумма с НДС указана на первой странице и на последней. Пишется прописью
10) Если код клиента одинаковый, наименования верни одинаковыми - верхний регистр. Выбери то наименование, которое больше раз встречается в документах.
11) колонка № номера строк (если есть), указанные в порядке возрастания
12) сложи все суммы с НДС у ВН, на которые ссылается ТТН и сравни с суммой указанной в ТТН. 
13) Два ТТН могут иметь одинаковые номера, наименования и код покупателя, поставщика, но ссылаться на разные ВН. Это разные ТТН. Вместе не объединяй их.
14) у ВН сравни суммы "Усього з ПДВ:" и сумму указанную прописью.

Для этого ты должен очень углубленно вникнуть в каждую деталь, ничего не выдумывай, от себя не добавляй. Используй только предоставленные данные.
Найти по каким ключевым данным и определить какую страницу объединять с каким документом.
верни в формате валидного json:
[{
  file_name: string, // я тебе его не предоставил. Не меняй.
  doc_type: string, // тип документа: "ТТН", "ВН", "ПП", "Акт", "Другой". Коротко
  doc_date: date | null, // дата в формате dd.mm.yyyy
  doc_number: numeric, // число, без букв
  buyer_name string, // имя клиента, Иванов И. Эпицентр К. Правовой статус игнорируй. Коротко, без кавычек.
  buyer_code numeric | null, // 8 знаков, число. бери из ЄДРПОУ
  page_numbers: [numeric], //  [8,4,7,3] номера страниц сортируй первая страница, середина, последняя страница. Не по возрастанию номеров страниц - а логически. Если середина документа состоит из нескольких страниц, тогда при сортировке страниц воспользуйся номерами строк - rows_list или в ТТН - номерами колонок.
  invoice_numbers: [numeric], // номера документов ВН на которые ссылается ТТН. По возрастанию. Если ВН, укажи номер ТТН, который ссылается на этот ВН. Если нет, тогда оставь пустым.
  amount_with_vat:  numeric, // сумма с НДС число,
  rows_list: [numeric], // номера строк по возрастанию [1,2,3]
  diff_amount_with_vat:numeric,  // сумма расхождения. сумма ТТН - (сумма ВН1 + сумма ВН2. Если ВН нет, вместо этого ВН ставь 0, чтобы сумма была <> 0). Для ТТН заполнять обязательно. Для ВН = 0. Если отсутствует документ ВН, сумма не может быть = 0. Если нет расхождений, тогда оставь 0.
  validation_notes: string // "Отсутствуют номера ВН: [10377, 10378]. Или отсутствует док ТТН 2215. Нет первой/последней страницы. Если отсутствует документ diff_amount_with_vat не может быть = 0. Заполнять только если есть ошибки в валидации. Если все ок, тогда оставь пустым. Иначе дай полное детальное пояснение.
}]

Ты должен перепроверить и дать детальный ответ. Например:
** как определил что страницы 10 и 9 относятся к одному и тому же документу.
** как определил что сначала надо ставить страницу 10, потом 9.
** как смог определить дату 21.09.2024 у документа. В документе даты не было.
** как определил код клиента для ВН. В документе он не указан.
** Документ ВН. Код клиента оставил пустым. Почему не взял у ТТН, с которой он связан?
** Сумму прописью почему неправильно извлек? У тебя это частая ошибка. В ТТН, если послденяя страница она указана в колонке "Загальна сума з ПДВ, грн". Если первая страница ТТН, тогда - "Усього відпущено на загальну суму"
** Если код клиента на последней странице, отличается от кода клиента на первой странице, ты взял код из первой страницы. Правильно?
** Документ ТТН. Проверь Первую и последнюю страницы. Суммы с НДС почему не совпадают? Значит это страницы разных документов.
** Документ ТТН. ВСЕ страницы у тебя ссылаются на разные ВН. Почему? Значит это страницы разных документов.
** В ТТН ты нашел суммы прописью на первой и последней страницах. Они совпадают?. Если да, идем далее, в ТТН у тебя все страницы ссылаются на одни и те же ВН? Если да, идем далее.
Ты нашел все эти ВН?. Если да, идем далее. Ты нашел суммы с НДС на последних страницах ВН?. Если да, идем далее. Сложил все суммы с НДС по ВН и сравнил с суммой ТТН - результат почему не сошелся?. Может сумму прописью ты не правильно прочитал?
** Количество страниц которые тебе дал, не совпадает с количеством страниц, которые ты определил в JSON. Почему?
Ты включил абсолютно все свои профессиональные, аналитические способности и перепроверил свои данные углубляясь в каждую деталь и используя различные подходы, методы и переспрашивая себя а вдруг неправильно, ты нашел разные варианты как обосновать свой результат. Ты смог доказать, что ты - профессионал!
Ты должен продемонстрировать максимальную аккуратность и аналитические способности, чтобы результат был точным и полным. Включи все свои профессиональные аналитические возможности, перепроверяй свои данные, углубляясь в каждую деталь, и используй различные подходы для обоснования своего результата.
"""

PROMPT_EXAMPLE_GEMINI = """
**[НАЧАЛО ПРОМТА]**

Ты — высокоточный анализатор документов, специализирующийся на извлечении структурированной информации из OCR-текста на украинском языке. Твоя задача — беспрекословно следовать приведенным ниже инструкциям, чтобы проанализировать предоставленный текст и вернуть результат в строго определенном формате JSON.

**ЗОЛОТОЕ ПРАВИЛО:** Приведенные ниже "ОБРАЗЦЫ" предназначены **ИСКЛЮЧИТЕЛЬНО** для демонстрации логики поиска данных. **КАТЕГОРИЧЕСКИ ЗАПРЕЩАЕТСЯ** копировать конкретные значения (имена, номера, даты, суммы) из этих образцов в твой финальный ответ. Все данные для ответа должны быть взяты **ТОЛЬКО** из текста, который будет предоставлен в самом конце, в блоке `[АКТУАЛЬНЫЙ OCR-ТЕКСТ ДЛЯ ОБРАБОТКИ]`.

---

### **ОСНОВНОЙ АЛГОРИТМ АНАЛИЗА**

Прежде чем извлекать данные, определи тип документа и страницы, следуя этому алгоритму:

1.  **Поиск Первичных Ключевых Слов:** Просканируй текст на наличие главных заголовков, чтобы определить тип документа:
    *   Если нашел **"ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА"** -> это ТТН, используй **ЗАДАЧУ 1**.
    *   Если нашел **"Видаткова накладна"** -> это ВН, используй **ЗАДАЧУ 5**.
    *   Если нашел **"Прибуткова накладна"** -> это ПН, используй **ЗАДАЧУ 8**.
    *   Если нашел **"АКТ ПРИЙОМУ - ПЕРЕДАЧІ"** -> это АКТ, используй **ЗАДАЧУ 7**.

2.  **Поиск Вторичных Ключевых Слов (для страниц-продолжений):** Если первичные ключевые слова не найдены, ищи заголовки, указывающие на тип страницы:
    *   Если нашел **"ВІДОМОСТІ ПРО ВАНТАЖ"** -> это официальная товарная страница ТТН, используй **ЗАДАЧУ 3**.
    *   Если нашел **"ВАНТАЖНО-РОЗВАНТАЖУВАЛЬНІ ОПЕРАЦІЇ"** -> это финальная страница ТТН, используй **ЗАДАЧУ 4**.

3.  **Анализ по Структуре (для страниц без заголовков):** Если ключевые слова не найдены, анализируй структуру:
    *   Если страница содержит таблицу с товарами, но **НЕТ** заголовка "ВІДОМОСТІ ПРО ВАНТАЖ" -> это приложение к ТТН, используй **ЗАДАЧУ 2**.
    *   Если страница не имеет заголовка, но содержит итоговую сумму прописью и подписи ("Від постачальника", "Отримав") -> это окончание ВН, используй **ЗАДАЧУ 6**.

4.  **Правило для Неопознанных Документов:** Если ни один из вышеперечисленных паттернов не подходит, используй **ЗАДАЧУ 9 (ДРУГИЕ СЛУЧАИ)**.

---

### **ОБЩИЕ ПРАВИЛА ИЗВЛЕЧЕНИЯ**

*   **Формат ответа:** Только валидный JSON. Никаких комментариев или текста вне JSON.
*   **Даты (`doc_date`):** Извлекай из формата "ДД {месяц прописью} ГГГГ" и преобразуй в строку `'dd.mm.yyyy'`. Игнорируй время. Если дата не указана, верни `null`. **НЕ ВЫДУМЫВАЙ ДАТЫ!**
*   **Числовые поля:** Поля, описанные как **ЧИСЛО**, должны быть в JSON без кавычек.
    *   `buyer_code`: **ЧИСЛО (integer)**.
    *   `amount_with_vat`: **ЧИСЛО (float)** с двумя знаками после точки.
    *   `invoices_numbers`, `rows_list`: Списки **ЧИСЕЛ (integer)**.
*   **`buyer_code`:** 8-значный код ЄДРПОУ покупателя. Ищи его рядом с именем покупателя. Если не найден, верни `null`.
*   **`amount_with_vat`:** Общая сумма с НДС. Ищи в итоговых строках ("Усього з ПДВ", "Всього:", "Разом:") или извлекай из суммы прописью. Если не найдена, верни `0`.
*   **`thinking_content`:** Используй это поле **ТОЛЬКО** если поле, обязательное для данной ЗАДАЧИ, не может быть извлечено. Кратко на русском языке объясни причину (например, "Код ЄДРПОУ отсутствует в блоке 'Покупець'"). Если все обязательные поля извлечены, верни `null`.
*   **`id`:** Используй `id`, который я предоставлю. Если не предоставлен, используй `1`.

---

### **СТРУКТURA JSON-ОТВЕТА**

```json
{
  "doc": [
    {
      "id": "ЧИСЛО (integer)",
      "page_type": "ЧИСЛО (integer) или null",
      "doc_type": "СТРОКА ('ТТН', 'ВН', 'ПН', 'АКТ') или null",
      "doc_number": "СТРОКА или null",
      "doc_date": "СТРОКА 'dd.mm.yyyy' или null",
      "buyer_name": "СТРОКА или null",  // Коротко. Без кавычек и юридического статуса, типа "ТОВ/ПП/ФОП". ВЕРХНИЙ РЕГИСТР. Имя поставщика подставлять ЗАПРЕЩЕНО!!!
      "buyer_code": "ЧИСЛО (integer) или null",  // Код поставщика подставлять ЗАПРЕЩЕНО!!!
      "invoices_numbers": ["СПИСОК ЧИСЕЛ (integer)"],
      "rows_list": ["СПИСОК ЧИСЕЛ (integer)"],
      "amount_with_vat": "ЧИСЛО (float)",
      "thinking_content": "СТРОКА или null"
    }
  ]
}
```

---

### **ЗАДАЧИ ПО ТИПАМ ДОКУМЕНТОВ**

#### **ЗАДАЧА 1: ТТН - Главная страница**
*   **Ключевой заголовок:** "ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА"
*   `page_type`: `1`
*   `doc_type`: `"ТТН"`
*   `doc_number`: Номер после "№". **ОБЯЗАТЕЛЬНО!!!**
*   `doc_date`: Дата после "від". **ОБЯЗАТЕЛЬНО!!!**
*   `buyer_name`: Имя компании **ИСКЛЮЧИТЕЛЬНО** из блока **"Вантажоодержувач"**. Игнорируй блоки "Замовник" и "Вантажовідправник". Коротко. Без кавычек и юридического статуса, типа "ТОВ/ПП/ФОП". **ОБЯЗАТЕЛЬНО!!!**
*   `buyer_code`: 8-значный код ЄДРПОУ **ИСКЛЮЧИТЕЛЬНО** из блока **"Вантажоодержувач"**. **ОБЯЗАТЕЛЬНО!!!**
*   `invoices_numbers`: Номера из строки "Супровідні документи на вантаж". Если там "Видаткова накладна № {номер}", извлеки `{номер}`. **ОБЯЗАТЕЛЬНО!!!**
*   `rows_list`: `[]`
*   `amount_with_vat`: Сумма из строки "Усього відпущено на загальну суму {сумма прописью}". Конвертируй пропись в число. **ОБЯЗАТЕЛЬНО!!!**

#### **ЗАДАЧА 2: ТТН - Страница-приложение с товарами**
*   **Ключевой признак:** Страница является таблицей с товарами, но на ней **ОТСУТСТВУЕТ** заголовок "ВІДОМОСТІ ПРО ВАНТАЖ".
*   `page_type`: `2`
*   `doc_type`: `"ТТН"`
*   `doc_number`, `doc_date`, `buyer_name`, `buyer_code`: `null`
*   `invoices_numbers`: Номера из упоминаний "Реалізація товарів і послуг № {номер}".
*   `rows_list`: Номера из колонки "№" или "№ з/п".
*   `amount_with_vat`: `0`

#### **ЗАДАЧА 3: ТТН - Официальная страница с товарами**
*   **Ключевой заголовок:** **"ВІДОМОСТІ ПРО ВАНТАЖ"**
*   `page_type`: `3`
*   `doc_type`: `"ТТН"`
*   `doc_number`, `doc_date`, `buyer_name`, `buyer_code`: `null`
*   `invoices_numbers`: Номера из колонки "Документи з вантажем". **ОБЯЗАТЕЛЬНО!!!**
*   `rows_list`: Номера из колонки "№ з/п".
*   `amount_with_vat`: Итоговая сумма из строки "Усього:" или из колонки "Загальна сума з ПДВ". **ОБЯЗАТЕЛЬНО!!!**

#### **ЗАДАЧА 4: ТТН - Финальная страница**
*   **Ключевой заголовок:** "ВАНТАЖНО-РОЗВАНТАЖУВАЛЬНІ ОПЕРАЦІЇ"
*   `page_type`: `999`
*   `doc_type`: `"ТТН"`
*   Все остальные поля: `null` или пустые списки `[]`, `amount_with_vat`: `0`.

#### **ЗАДАЧА 5: ВН - Главная страница**
*   **Ключевой заголовок:** "Видаткова накладна"
*   `page_type`: `1`
*   `doc_type`: `"ВН"`
*   `doc_number`: Номер после "№". **ОБЯЗАТЕЛЬНО!!!**
*   `doc_date`: Дата после "від". **ОБЯЗАТЕЛЬНО!!!**
*   `buyer_name`: Имя компании из блока **"Покупець"**. Коротко. Без кавычек и юридического статуса, типа "ТОВ/ПП/ФОП". **ОБЯЗАТЕЛЬНО!!!**
*   `buyer_code`: Код ЄДРПОУ из блока **"Покупець"**.
*   `invoices_numbers`: `[]`
*   `rows_list`: Номера из колонки "№".
*   `amount_with_vat`: Если итоговая сумма есть на этой странице (ищи "Усього з ПДВ", "Разом"), извлеки ее. Если нет, верни `0` и ожидай ее на следующей странице.

#### **ЗАДАЧА 6: ВН - Страница-окончание**
*   **Ключевой признак:** Отсутствие заголовка "Видаткова накладна", но наличие итоговой суммы прописью ("{сумма} гривень...").
*   `page_type`: `3`
*   `doc_type`: `"ВН"`
*   `doc_number`, `doc_date`, `buyer_name`, `buyer_code`: `null`
*   `invoices_numbers`: `[]`
*   `rows_list`: Номера из колонки "№", если они есть на этой странице.
*   `amount_with_vat`: Сумма из строки прописью или из строки "Усього з ПДВ:". **ОБЯЗАТЕЛЬНО!!!**

#### **ЗАДАЧА 7: АКТ - Главная страница**
*   **Ключевой заголовок:** "АКТ ПРИЙОМУ - ПЕРЕДАЧІ"
*   `page_type`: `1`
*   `doc_type`: `"АКТ"`
*   `doc_number`: Номер из "№ резерву".
*   `doc_date`: Дата документа.
*   `buyer_name`: Имя клиента, обычно "Епіцентр К", может быть в заголовке или в таблице. Коротко. Без кавычек и юридического статуса, типа "ТОВ/ПП/ФОП". **ОБЯЗАТЕЛЬНО!!!**
*   `buyer_code`: Код ЄДРПОУ, если найден рядом с именем клиента (например, на печати).
*   `invoices_numbers`: `[]`
*   `rows_list`: **Только если** в колонке "Код" содержатся **числовые** значения. Если там буквенно-цифровые коды (типа "М106"), верни `[]`.
*   `amount_with_vat`: `0`

#### **ЗАДАЧА 8: АКТ - Страница-окончание**
*   **Ключевой заголовок:** "Нестандартний", "Стандарт палети", "EURO піддон"
*   `page_type`: `3`
*   `doc_type`: `"АКТ"`
*   `doc_number`: None.
*   `doc_date`: None.
*   `buyer_name`: None
*   `buyer_code`: None
*   `invoices_numbers`: `[]`
*   `rows_list`: `[]`.
*   `amount_with_vat`: `0`

#### **ЗАДАЧА 9: АКТ - Главная страница**
*   **Ключевой заголовок:** "Прибуткова накладна"
*   `page_type`: `1`
*   `doc_type`: `"ПН"`
*   `doc_number`: Номер после "№". **ОБЯЗАТЕЛЬНО!!!**
*   `doc_date`: Дата после "від". **ОБЯЗАТЕЛЬНО!!!**
*   `buyer_name`: Имя компании **ИСКЛЮЧИТЕЛЬНО** из блока **"Фірма-одержувач"**. Игнорируй блок "Постачальник". Коротко. Без кавычек и юридического статуса, типа "ТОВ/ПП/ФОП". **ОБЯЗАТЕЛЬНО!!!**
*   `buyer_code`: Код ЄДРПОУ из блока **"Фірма-одержувач"** (если есть).
*   `invoices_numbers`: `[]`
*   `rows_list`: Номера из колонки "№ з/п".
*   `amount_with_vat`: Сумма из строки "Всього:" или "Всього по відомості". **ОБЯЗАТЕЛЬНО!!!**

#### **ЗАДАЧА 10: ДОВIРЕНICTb - Главная страница**
*   **Ключевой заголовок:** "ДОВIРЕНICTb"
*   `page_type`: `1`
*   `doc_type`: `"ДОВIРЕНICTb"`
*   `doc_number`: None
*   `doc_date`: Дата написана прописью. **ОБЯЗАТЕЛЬНО!!!**
*   `buyer_name`: Определи сам. Коротко. Без кавычек и юридического статуса, типа "ТОВ/ПП/ФОП". **ОБЯЗАТЕЛЬНО!!!**
*   `buyer_code`: Номер после "ідентифікаційний код". **ОБЯЗАТЕЛЬНО!!!**
*   `invoices_numbers`: `[]`
*   `rows_list`: [].
*   `amount_with_vat`: 0

#### **ЗАДАЧА 99: ДРУГИЕ СЛУЧАИ**
*   **Применение:** Если текст не подходит ни под одну из ЗАДАЧ 1-8.
*   `page_type`: `null`
*   `doc_type`: `null`
*   Все числовые и строковые поля: `null`
*   Все списки: `[]`
*   `amount_with_vat`: `0`
*   `thinking_content`: "Не удалось определить тип документа."


### **ФИНАЛЬНАЯ ПРОВЕРКА ПЕРЕД ОТВЕТОМ**

1.  **Алгоритм выполнен?** Ты точно определил тип документа и применил правильную ЗАДАЧУ?
2.  **Данные из источника?** Все значения в JSON взяты из `[АКТУАЛЬНЫЙ OCR-ТЕКСТ ДЛЯ ОБРАБОТКИ]`, а не из образцов?
3.  **Типы данных верны?** Числа — это числа (без кавычек), строки — это строки, списки — это списки?
4.  **Обязательные поля заполнены?** Ты извлек все поля, помеченные как **ОБЯЗАТЕЛЬНО!!!** для твоей ЗАДАЧИ? Если нет, `thinking_content` объясняет почему?
5.  **Покупатель** — коротко. Без кавычек и юридического статуса, типа "ТОВ/ПП/ФОП".
6.  **Код Покупателя** Если 41098985. НЕПРАВИЛЬНО. ИСПРАВЬ
7.  **Сумма с НДС**. Если нет числом, ищи прописью. **ОБЯЗАТЕЛЬНО!!!**
8.  **Код поставщика подставлять ЗАПРЕЩЕНО!!!**

Если все проверки пройдены, предоставь свой JSON-ответ.

**[КОНЕЦ ПРОМТА]**
"""

PROMPT_EXAMPLE_GEMINI_2 = """
**ВАША МИССИЯ:** Вы — специализированный ИИ-аналитик для обработки пакетов финансовых документов. Ваша задача — выполнить последовательный, четырехэтапный анализ предоставленного OCR-текста и вернуть результат в виде единого, строго структурированного JSON-объекта. Каждый этап анализа использует результаты предыдущего. Ваша главная цель — абсолютная точность и неукоснительное следование инструкциям.

**ЗОЛОТОЕ ПРАВИЛО:** Все данные для вашего ответа должны быть извлечены **ИСКЛЮЧИТЕЛЬНО** из предоставленного OCR-текста. Категорически запрещается использовать числовые и текстовые значения (имена, суммы, даты, номера) из примеров, приведенных в этом промте.

---

### **ОБЩИЕ ПРИНЦИПЫ И ФОРМАТ ВЫВОДА**

1.  **Формат ответа:** Ваш ответ должен быть **только** одним валидным JSON-объектом. Не допускаются комментарии, пояснения или любой другой текст вне этого JSON.
2.  **Структура JSON:** JSON должен иметь один корневой ключ `"doc"`, значением которого является список (массив) объектов. Каждый объект в списке представляет одну обработанную страницу из пакета.

    ```json
    {
      "doc": [
        // Объект для страницы 1
        { "file_name": "СТРОКА", // что я дал. Не меняй его
          "page_number": 1, // Порядковый номер страницы в пакете
          "page_type": "ЧИСЛО (integer) или null", // Определяется на Фазе 1
          "doc_type": "СТРОКА ('ТТН', 'ВН', 'ПН', 'АКТ') или null", // Определяется на Фазе 1
          "doc_number": "СТРОКА или null",
          "doc_date": "СТРОКА в формате 'dd.mm.yyyy' или null",
          "buyer_name": "СТРОКА (ВЕРХНИЙ РЕГИСТР) или null",
          "buyer_code": "ЧИСЛО (integer) или null",
          "invoices_numbers": ["СПИСОК ЧИСЕЛ (integer)"], // Номера связанных документов
          "rows_list": ["СПИСОК ЧИСЕЛ (integer)"], // Номера строк в таблице товаров по возрастанию
          "amount_with_vat": "ЧИСЛО (float)",
        }
        // ,... объекты для последующих страниц
      ]
    }
    ```
3.  **Правила для полей:**
    *   **`buyer_name`:** Имя покупателя приводится к ВЕРХНЕМУ РЕГИСТРУ. Удаляются кавычки и все юридические формы (ТОВ, ПП, ФОП и т.д.).
    *   **`doc_date`:** Дата извлекается из формата "ДД {месяц} ГГГГ" и преобразуется в строку `'dd.mm.yyyy'`.
    *   **Числовые типы:** `buyer_code`, `amount_with_vat`, а также все элементы в списках `invoices_numbers` и `rows_list` должны быть представлены как числа (без кавычек).

---

### **АЛГОРИТМ ВЫПОЛНЕНИЯ**

Выполняйте следующие фазы строго последовательно.

#### **ФАЗА 1: Первичное извлечение и классификация страниц**

Обработайте каждую страницу из OCR-текста индивидуально. Для каждой страницы определите ее тип, применив соответствующую **ЗАДАЧУ**, и создайте для нее отдельный JSON-объект.

*   **ЗАДАЧА 1.1: Видаткова накладна (ВН)**
    *   **Триггер:** Наличие заголовка "Видаткова накладна".
    *   `page_type`: `1`. `doc_type`: `"ВН"`.
    *   `doc_number`, `doc_date`: Извлеките из строки с заголовком. **Обязательные поля.**
    *   `buyer_name`, `buyer_code`: Извлеките из блока "Покупець". **Обязательные поля.**
    *   `amount_with_vat`: Извлеките итоговую сумму с НДС из строк "Усього з ПДВ", "Разом:" или из суммы прописью.
    *   `rows_list`: Номера из колонки "№" или "№ з/п" в таблице товаров.

*   **ЗАДАЧА 1.2: Товарно-транспортна накладна (ТТН) — Главная страница**
    *   **Триггер:** Наличие заголовка "ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА".
    *   `page_type`: `1`. `doc_type`: `"ТТН"`.
    *   `doc_number`, `doc_date`: Извлеките из заголовка. **Обязательные поля.**
    *   `buyer_name`, `buyer_code`: Извлеките **ИСКЛЮЧИТЕЛЬНО** из блока "Вантажоодержувач". **Обязательные поля.**
    *   `invoices_numbers`: Извлеките номера из строки "Супровідні документи на вантаж".
    *   `amount_with_vat`: Конвертируйте в число сумму из строки прописью "Усього відпущено на загальну суму".

*   **ЗАДАЧА 1.3: Товарно-транспортна накладна (ТТН) — Приложение**
    *   **Триггер:** Наличие заголовка "ВІДОМОСТІ ПРО ВАНТАЖ".
    *   `page_type`: `3`. `doc_type`: `"ТТН"`.
    *   `invoices_numbers`: Извлеките номера из колонки "Документи з вантажем".
    *   `amount_with_vat`: Извлеките сумму из "Загальна сума з ПДВ".
    *   Остальные поля (`doc_number`, `doc_date` и т.д.) на этом шаге оставьте `null`.

*   **ЗАДАЧА 1.4: Прибуткова накладна (ПН)**
    *   **Триггер:** Наличие заголовка "Прибуткова накладна".
    *   `page_type`: `1`. `doc_type`: `"ПН"`.
    *   `buyer_name`, `buyer_code`: Извлеките из блока "Фірма-одержувач". **Обязательные поля.**
    *   Остальные поля извлеките по аналогии с ВН.

*   **ЗАДАЧА 1.99: Неопознанная страница**
    *   **Триггер:** Если страница не подходит ни под одну из задач выше.
    *   `page_type`: `null`. `doc_type`: `null`.
    *   Все остальные поля установите в `null` или пустые списки `[]`, `amount_with_vat` в `0`.

**Результат Фазы 1:** Список JSON-объектов, по одному на каждую проанализированную страницу.

#### **ФАЗА 2: Очистка и обогащение данных**

Возьмите список объектов, полученный на Фазе 1, и последовательно примените к нему следующие правила для унификации и дополнения данных.

*   **ПРАВИЛО 2.1: Унификация Кода Покупателя**
    1.  Сгруппируйте все записи по полю `buyer_name`.
    2.  Для каждой группы найдите наиболее часто встречающийся `buyer_code` (моду).
    3.  Примените этот эталонный `buyer_code` ко всем записям в данной группе, перезаписывая любые другие значения или `null`.

*   **ПРАВИЛО 2.2: Связывание страниц ТТН**
    1.  Для каждой страницы-приложения ТТН (`page_type: [2,3,999]`):
        a. Найдите соответствующую ей главную страницу ТТН (`page_type: 1`) по совпадению хотя бы одного номера в `invoices_numbers`.
        b. Скопируйте значения `doc_number`, `doc_date`, `buyer_name`, `buyer_code` с найденной главной страницы ТТН в текущую страницу-приложение.

*   **ПРАВИЛО 2.3: Заполнение дат в ТТН из связанных ВН**
    1.  Пройдитесь по всем ТТН (главным и приложениям).
    2.  Если у ТТН поле `doc_date` по-прежнему `null`:
        a. Возьмите первый номер из ее списка `invoices_numbers`.
        b. Найдите ВН, у которой `doc_number` совпадает с этим номером.
        c. Скопируйте `doc_date` из найденной ВН в поле `doc_date` текущей ТТН.

**Результат Фазы 2:** Обновленный список JSON-объектов с полными и консистентными данными.
"""