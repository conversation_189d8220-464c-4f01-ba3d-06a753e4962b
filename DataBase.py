# подключения к базе PG для внесения информации сканированных файлах и страницах

import asyncpg
import asyncio
import os
import logging
from dotenv import dotenv_values

# Настройка логирования
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Загрузка переменных окружения
config = dotenv_values(".env")
DB_USER = config.get("PG_USER", "")
DB_PASSWORD = config.get("PG_PASSWORD", "")
DB_HOST = config.get("PG_HOST_LOCAL", "")
DB_PORT = config.get("PG_PORT", "")
DB_NAME = config.get("PG_DBNAME", "")

SIMILAR = "CREATE EXTENSION IF NOT EXISTS pg_trgm;"  # Создание расширения pg_trgm для поддержки функций похожести текста

if not DB_USER or not DB_PASSWORD or not DB_HOST or not DB_PORT or not DB_NAME:
    raise ValueError("Не все переменные окружения для базы данных указаны в файле .env")

TABLE_NAME = "t_scan_documents"
TABLE_NAME_RAW = "t_scan_documents_raw"


async def create_pool():
    try:
        pool = await asyncpg.create_pool(
            user=DB_USER,
            password=DB_PASSWORD,
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME,
        )
        return pool
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_tables: {e}")
        # return None


# создаем таблицу
async def create_tables(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
                f"""
                -- Таблица для хранения информации о сканированных документах
                CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
                    id SERIAL PRIMARY KEY,
                    doc_type varchar(255) NULL,
                    doc_date date NULL,
                    doc_number varchar(255) NULL,
                    buyer_name varchar(255) NULL,
                    buyer_code varchar(255) NULL,
                    page_type INT NULL,
                    invoices_numbers jsonb NULL,
                    amount_with_vat numeric(15,2) NULL DEFAULT 0,
                    rows_list jsonb NULL,
                    external_id INT NOT NULL,
                    page_number INT NULL,
                    page INT NULL,
                    sort INT NULL,
                    date_from_1c timestamp(0) NULL,
                    file_name varchar(255) NULL,
                    full_path varchar(255) NULL,
                    description TEXT NULL,
                    thinking_content TEXT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE (external_id)
                );

                COMMENT ON TABLE {TABLE_NAME} IS 'Таблица для хранения информации о сканерованных документах';
                COMMENT ON COLUMN {TABLE_NAME}.full_path IS 'Полный путь к файлу';
                COMMENT ON COLUMN {TABLE_NAME}.doc_type IS 'Тип';
                COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Номер';
                COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Дата1C';
                COMMENT ON COLUMN {TABLE_NAME}.page IS 'номер страницы в сортировке';
                COMMENT ON COLUMN {TABLE_NAME}.sort IS 'сортировка внутир документа';
                COMMENT ON COLUMN {TABLE_NAME}.description IS 'текст страницы';
                COMMENT ON COLUMN {TABLE_NAME}.created_at IS 'Дата создания записи';
                COMMENT ON COLUMN {TABLE_NAME}.buyer_code IS 'ОКПО';
                COMMENT ON COLUMN {TABLE_NAME}.buyer_name IS 'Покупатель';
                COMMENT ON COLUMN {TABLE_NAME}.date_from_1c IS 'Дата из 1С';
                COMMENT ON COLUMN {TABLE_NAME}.page_type IS 'тип страницы';
                COMMENT ON COLUMN {TABLE_NAME}.rows_list IS 'Список строк на странице';
                COMMENT ON COLUMN {TABLE_NAME}.external_id IS 'Внешний id';
                COMMENT ON COLUMN {TABLE_NAME}.page_number IS 'Номер страницы в исходном файле';
                COMMENT ON COLUMN {TABLE_NAME}.invoices_numbers IS 'Список номеров связанных ВН';
                COMMENT ON COLUMN {TABLE_NAME}.thinking_content IS 'логика мышления';
                COMMENT ON COLUMN {TABLE_NAME}.file_name IS 'Имя файла';
                COMMENT ON COLUMN {TABLE_NAME}.amount_with_vat IS 'Сумма с НДС';
                
                CREATE INDEX CONCURRENTLY idx_scan_docs_type_page_file 
                ON {TABLE_NAME}(doc_type, page_type, file_name);
                
                CREATE INDEX CONCURRENTLY idx_scan_docs_buyer_name 
                ON {TABLE_NAME}(buyer_name) WHERE buyer_code IS NOT NULL;
                
                CREATE INDEX CONCURRENTLY idx_scan_docs_invoices_gin 
                ON {TABLE_NAME} USING GIN(invoices_numbers);
                
                CREATE INDEX idx_buyer_code ON {TABLE_NAME} (buyer_code)
                WHERE buyer_code IS NOT NULL AND buyer_name IS NOT NULL;
                """
            )
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_tables: {e}")


async def create_function(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
        f"""
        CREATE OR REPLACE FUNCTION public.fn_t_scan_documents()
         RETURNS trigger
         LANGUAGE plpgsql
        AS $function$
            DECLARE
                v_basename TEXT;
                -- Добавленные переменные для нового кода
                temp_record RECORD;
                temp_buyer_code TEXT;
                v_current_id BIGINT;
                temp_buyer_name TEXT;
            BEGIN
        
                -- 4. Нормализация текстовых полей
                -- 4. Нормализация текстовых полей
                BEGIN
                    -- Очистка от управляющих символов
                    NEW.buyer_name := REGEXP_REPLACE(NEW.buyer_name, '[[:cntrl:]]', '', 'g');
                    NEW.buyer_code := REGEXP_REPLACE(NEW.buyer_code, '[[:cntrl:]]', '', 'g');
                    NEW.doc_number := REGEXP_REPLACE(NEW.doc_number, '[[:cntrl:]]', '', 'g');
                    
                    -- Основная нормализация
                    NEW.buyer_name := NULLIF(TRIM(BOTH FROM UPPER(NEW.buyer_name)), '');
                    NEW.buyer_code := NULLIF(TRIM(BOTH FROM NEW.buyer_code), '');
                    NEW.doc_number := NULLIF(TRIM(BOTH FROM NEW.doc_number), '');
                EXCEPTION
                    WHEN OTHERS THEN
                        -- Ошибки нормализации не должны прерывать выполнение
                        RAISE NOTICE 'Normalization error: %', SQLERRM;
                END;
        
                NEW.buyer_name := 
                    regexp_replace(
                        upper(NEW.buyer_name),
                        '[^А-ЯІЇЄҐ0-9a-zA-Z"" ]', '', 'g' -- Удаляем спецсимволы
                    );
        
                -- 1. Получаем данные из t_scan_documents_raw одним запросом
                SELECT
                    r.full_path,
                    r.page_number,
                    r.description,
                    r.file_name
                INTO
                    new.full_path,
                    new.page_number,
                    new.description,
                    new.file_name
                FROM t_scan_documents_raw r
                WHERE r.id = new.external_id;
                
                -- 2. Обработка JSON null: Преобразуем SQL NULL или JSON 'null' в пустой JSON-массив '[]'
                IF new.invoices_numbers IS NULL OR new.invoices_numbers = 'null'::jsonb THEN
                    new.invoices_numbers := '[]'::jsonb;
                END IF;
        
                IF new.rows_list IS NULL OR new.rows_list = 'null'::jsonb THEN
                    new.rows_list := '[]'::jsonb;
                END IF;
        
                -- 3. Вычисляем file_name из full_path (если full_path был получен или уже существовал)
                new.file_name := 
                  CASE 
                    WHEN new.full_path IS NOT NULL 
                    THEN regexp_replace(new.full_path, '^.*[\\/]', '') 
                    ELSE NULL 
                  END;
        
        --************************************
        -- добавленный код
        --************************************
                
                -- 5. Проверка и нормализация invoices_numbers
                IF new.invoices_numbers IS NOT NULL THEN
                    IF jsonb_typeof(new.invoices_numbers) != 'array' OR jsonb_array_length(new.invoices_numbers) = 0 THEN
                        new.invoices_numbers := NULL;
                    END IF;
                END IF;
                
                -- 6. Установка v_current_id (для исключения текущей записи в подзапросах)
                v_current_id := CASE 
                    WHEN TG_OP = 'UPDATE' THEN OLD.id 
                    ELSE NULL 
                END;
                
                -- 7. Обновление полей для документов
                BEGIN
                    -- ТТН страница 1: заполняем из ВН
                    IF NEW.doc_type = 'ТТН' AND NEW.invoices_numbers IS NOT NULL THEN
                        SELECT 
                            src.doc_date,
                            src.doc_number,
                            src.buyer_name,
                            src.buyer_code
                        INTO temp_record
                        FROM t_scan_documents src
                        WHERE src.doc_type = 'ВН'
                            AND src.file_name = NEW.file_name
                            AND src.doc_number IS NOT NULL
                            AND jsonb_path_exists(
                                NEW.invoices_numbers, 
                                '$[*] ? (@ == $val || @ == $val_str)', 
                                jsonb_build_object('val', src.doc_number::integer, 'val_str', src.doc_number::text)
                            )
                            AND src.id IS DISTINCT FROM v_current_id
                        ORDER BY src.id DESC
                        LIMIT 1;
                        
                        IF FOUND THEN
                            new.doc_date := COALESCE(new.doc_date, temp_record.doc_date);
                            new.doc_number := COALESCE(new.doc_number, temp_record.doc_number);
                            new.buyer_name := COALESCE(new.buyer_name, temp_record.buyer_name);
                            new.buyer_code := COALESCE(new.buyer_code, temp_record.buyer_code);
                        END IF;
                    
                    -- ТТН страница 3/999: заполняем из ТТН страницы 1
                    ELSIF NEW.doc_type = 'ТТН' AND NEW.page_type IN (3, 999) AND 
                          (NEW.invoices_numbers IS NOT NULL OR 
                           (NEW.doc_number IS NOT NULL AND NEW.doc_number <> '')) THEN
                        SELECT 
                            src.doc_date,
                            src.doc_number,
                            src.buyer_name,
                            src.buyer_code
                        INTO temp_record
                        FROM t_scan_documents src
                        WHERE src.doc_type = 'ТТН'
                          AND src.page_type = 1
                          AND src.file_name = NEW.file_name
                          AND src.id IS DISTINCT FROM v_current_id
                          AND (
                              -- Сравнение по invoices_numbers (если оба массива не пусты)
                              (jsonb_array_length(NEW.invoices_numbers) > 0 AND 
                               jsonb_array_length(src.invoices_numbers) > 0 AND 
                               (src.invoices_numbers->>0) = (NEW.invoices_numbers->>0))
                              
                              OR
                              
                              -- Сравнение по doc_number (если номер не пустой)
                              (NEW.doc_number IS NOT NULL AND NEW.doc_number <> '' AND 
                               src.doc_number IS NOT NULL AND src.doc_number <> '' AND 
                               src.doc_number::text = NEW.doc_number::text)
                          )
                        ORDER BY src.id DESC
                        LIMIT 1;
                        
                        IF FOUND THEN
                            new.doc_date := COALESCE(new.doc_date, temp_record.doc_date);
                            new.doc_number := COALESCE(new.doc_number, temp_record.doc_number);
                            new.buyer_name := COALESCE(new.buyer_name, temp_record.buyer_name);
                            new.buyer_code := COALESCE(new.buyer_code, temp_record.buyer_code);
                        END IF;
                    
                    -- ВН: заполняем из ТТН страницы 1
                    ELSIF new.doc_type = 'ВН' AND new.doc_number IS NOT NULL THEN
                        SELECT 
                            src.doc_date,
                            src.buyer_name,
                            src.buyer_code
                        INTO temp_record
                        FROM t_scan_documents src
                        WHERE src.doc_type = 'ТТН'
                          AND src.page_type = 1
                          AND src.file_name = new.file_name
        --                  AND src.invoices_numbers ? new.doc_number::text
                          AND jsonb_path_exists(
                                src.invoices_numbers, 
                                '$[*] ? (@ == $val || @ == $val_str)', 
                                jsonb_build_object('val', new.doc_number::integer, 'val_str', new.doc_number::text)
                            )
        
                          AND src.id IS DISTINCT FROM v_current_id
                        ORDER BY src.id DESC
                        LIMIT 1;
                        
                        IF FOUND THEN
                            new.doc_date := COALESCE(new.doc_date, temp_record.doc_date);
                            new.buyer_name := COALESCE(new.buyer_name, temp_record.buyer_name);
                            new.buyer_code := COALESCE(new.buyer_code, temp_record.buyer_code);
                        END IF;
                    END IF;
        
                    -- 8. Заполнение пустого buyer_code
                    IF NEW.buyer_code IS NULL AND NEW.buyer_name IS NOT NULL THEN
                        -- Пытаемся найти код по имени
                        SELECT buyer_code INTO temp_buyer_code
                        FROM (
                            SELECT buyer_code, COUNT(*) AS usage_count
                            FROM t_scan_documents
                            WHERE buyer_name = NEW.buyer_name
                              AND buyer_code IS NOT NULL
                              AND buyer_code <> ''
                              AND id IS DISTINCT FROM v_current_id
                            GROUP BY buyer_code
                            ORDER BY usage_count DESC, buyer_code
                            LIMIT 1
                        ) most_used;
                        
                        -- Если не нашли, пытаемся найти в справочнике
                        IF temp_buyer_code IS NULL THEN
                            SELECT code INTO temp_buyer_code
                            FROM buyer_codes
                            WHERE name = NEW.buyer_name
                            LIMIT 1;
                        END IF;
                        
                        NEW.buyer_code := temp_buyer_code;
                    END IF;
        
                    -- 9. Нормализация buyer_name по самому частому варианту для buyer_code
                    -- 9. Нормализация buyer_name по самому частому НЕпустому варианту для buyer_code
                    IF NEW.buyer_code IS NOT NULL THEN
                        -- Стадия 1: Пытаемся найти любое непустое имя для этого кода
                        SELECT buyer_name INTO temp_buyer_name
                        FROM t_scan_documents
                        WHERE buyer_code = NEW.buyer_code
                          AND buyer_name IS NOT NULL
                          AND buyer_name <> ''
                          AND id IS DISTINCT FROM v_current_id
                        ORDER BY id DESC  -- Берем последнее добавленное имя
                        LIMIT 1;
                        -- Стадия 2: Если не нашли в основной таблице, используем резервный вариант
                        IF temp_buyer_name IS NOT NULL THEN
                            NEW.buyer_name := temp_buyer_name;
                        ELSE
                            -- Резервный вариант: Используем имя из той же записи, если оно есть
                            IF NEW.buyer_name IS NOT NULL AND NEW.buyer_name <> '' THEN
                                -- Оставляем текущее значение
                            ELSE
                                -- Если все варианты исчерпаны, используем значение из словаря
                                SELECT name INTO NEW.buyer_name
                                FROM buyer_codes
                                WHERE code = NEW.buyer_code
                                LIMIT 1;
                            END IF;
                        END IF;
                    END IF;
        
        
                    EXCEPTION
                        WHEN OTHERS THEN
                            RAISE EXCEPTION 'Trigger error in fn_t_scan_documents for doc_type=%, page_type=%, file=%: %', 
                                new.doc_type, new.page_type, new.file_name, SQLERRM;
                END;
        
                --10. Извлекаем из номера только цифры
                new.doc_number = (
                    SELECT regexp_replace(
                        regexp_replace(
                            CASE 
                                WHEN new.doc_number LIKE '%3000%' THEN REPLACE(new.doc_number, '3000', '')
                                ELSE new.doc_number
                            END,
                            '^.*?(\d+)\D*$',
                            '\1'
                        ),
                        '^0+(\d+)$',
                        '\1'
                    )) 
                ;
        
                RETURN NEW;
            END;
            $function$
        ;
        """
            )
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_function: {e}")


async def create_function_base(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
                """
    CREATE OR REPLACE FUNCTION public.fn_t_scan_documents()
     RETURNS trigger
     LANGUAGE plpgsql
    AS $function$
        DECLARE
            v_basename TEXT;
            temp_record RECORD;
            temp_buyer_code TEXT;
            v_current_id BIGINT;
            temp_buyer_name TEXT;
        BEGIN
            -- 4. Нормализация текстовых полей
            BEGIN
                -- Очистка от управляющих символов
                NEW.buyer_name := REGEXP_REPLACE(COALESCE(NEW.buyer_name, ''), '[[:cntrl:]]', '', 'g');
                NEW.buyer_code := REGEXP_REPLACE(COALESCE(NEW.buyer_code, ''), '[[:cntrl:]]', '', 'g');
                NEW.doc_number := REGEXP_REPLACE(COALESCE(NEW.doc_number, ''), '[[:cntrl:]]', '', 'g');
                
                -- Основная нормализация
                NEW.buyer_name := NULLIF(TRIM(BOTH FROM UPPER(NEW.buyer_name)), '');
                NEW.buyer_code := NULLIF(TRIM(BOTH FROM NEW.buyer_code), '');
                NEW.doc_number := NULLIF(TRIM(BOTH FROM NEW.doc_number), '');
            EXCEPTION
                WHEN OTHERS THEN
                    RAISE NOTICE 'Normalization error: %', SQLERRM;
            END;
    
            -- Дополнительная очистка имени
            NEW.buyer_name := regexp_replace(
                COALESCE(NEW.buyer_name, ''),
                '[^А-ЯІЇЄҐ0-9a-zA-Z"" ]', '', 'g'
            );
    
            -- 1. Получаем данные из t_scan_documents_raw
            SELECT
                r.full_path,
                r.page_number,
                r.description,
                r.file_name
            INTO
                new.full_path,
                new.page_number,
                new.description,
                new.file_name
            FROM t_scan_documents_raw r
            WHERE r.id = new.external_id;
            
            -- 2. Обработка JSON null
            IF new.invoices_numbers IS NULL OR new.invoices_numbers = 'null'::jsonb THEN
                new.invoices_numbers := '[]'::jsonb;
            END IF;
    
            IF new.rows_list IS NULL OR new.rows_list = 'null'::jsonb THEN
                new.rows_list := '[]'::jsonb;
            END IF;
    
            -- 3. Вычисляем file_name из full_path
            new.file_name := 
              CASE 
                WHEN new.full_path IS NOT NULL 
                THEN regexp_replace(new.full_path, '^.*[\\/]', '') 
                ELSE COALESCE(new.file_name, NULL) 
              END;
    
            -- 5. Проверка и нормализация invoices_numbers
            IF new.invoices_numbers IS NOT NULL THEN
                IF jsonb_typeof(new.invoices_numbers) != 'array' OR jsonb_array_length(new.invoices_numbers) = 0 THEN
                    new.invoices_numbers := NULL;
                END IF;
            END IF;
            
            -- 6. Установка v_current_id
            v_current_id := CASE 
                WHEN TG_OP = 'UPDATE' THEN OLD.id 
                ELSE NULL 
            END;
            
            -- 7. Обновление полей для документов
            BEGIN
                -- ТТН страница 1: заполняем из ВН
                IF NEW.doc_type = 'ТТН' AND NEW.invoices_numbers IS NOT NULL THEN
                    SELECT 
                        src.doc_date,
                        src.doc_number,
                        src.buyer_name,
                        src.buyer_code
                    INTO temp_record
                    FROM t_scan_documents src
                    WHERE src.doc_type = 'ВН'
                        AND src.file_name = NEW.file_name
                        AND src.doc_number IS NOT NULL
                        AND jsonb_path_exists(
                            NEW.invoices_numbers, 
                            '$[*] ? (@ == $val || @ == $val_str)', 
                            jsonb_build_object('val', src.doc_number::integer, 'val_str', src.doc_number::text)
                        )
                        AND src.id IS DISTINCT FROM v_current_id
                    ORDER BY src.id DESC
                    LIMIT 1;
                    
                    IF FOUND THEN
                        new.doc_date := COALESCE(new.doc_date, temp_record.doc_date);
                        new.doc_number := COALESCE(new.doc_number, temp_record.doc_number);
                        new.buyer_name := COALESCE(new.buyer_name, temp_record.buyer_name);
                        new.buyer_code := COALESCE(new.buyer_code, temp_record.buyer_code);
                    END IF;
                
                -- ТТН страница 3/999: заполняем из ТТН страницы 1
                ELSIF NEW.doc_type = 'ТТН' AND NEW.page_type IN (3, 999) AND 
                      (NEW.invoices_numbers IS NOT NULL OR 
                       (NEW.doc_number IS NOT NULL AND NEW.doc_number <> '')) THEN
                    SELECT 
                        src.doc_date,
                        src.doc_number,
                        src.buyer_name,
                        src.buyer_code
                    INTO temp_record
                    FROM t_scan_documents src
                    WHERE src.doc_type = 'ТТН'
                      AND src.page_type = 1
                      AND src.file_name = NEW.file_name
                      AND src.id IS DISTINCT FROM v_current_id
                      AND (
                          (jsonb_array_length(NEW.invoices_numbers) > 0 AND 
                           jsonb_array_length(src.invoices_numbers) > 0 AND 
                           (src.invoices_numbers->>0) = (NEW.invoices_numbers->>0))
                          OR
                          (NEW.doc_number IS NOT NULL AND NEW.doc_number <> '' AND 
                           src.doc_number IS NOT NULL AND src.doc_number <> '' AND 
                           src.doc_number::text = NEW.doc_number::text)
                      )
                    ORDER BY src.id DESC
                    LIMIT 1;
                    
                    IF FOUND THEN
                        new.doc_date := COALESCE(new.doc_date, temp_record.doc_date);
                        new.doc_number := COALESCE(new.doc_number, temp_record.doc_number);
                        new.buyer_name := COALESCE(new.buyer_name, temp_record.buyer_name);
                        new.buyer_code := COALESCE(new.buyer_code, temp_record.buyer_code);
                    END IF;
                
                -- ВН: заполняем из ТТН страницы 1
                ELSIF new.doc_type = 'ВН' AND new.doc_number IS NOT NULL THEN
                    SELECT 
                        src.doc_date,
                        src.buyer_name,
                        src.buyer_code
                    INTO temp_record
                    FROM t_scan_documents src
                    WHERE src.doc_type = 'ТТН'
                      AND src.page_type = 1
                      AND src.file_name = new.file_name
                      AND jsonb_path_exists(
                            src.invoices_numbers, 
                            '$[*] ? (@ == $val || @ == $val_str)', 
                            jsonb_build_object('val', new.doc_number::integer, 'val_str', new.doc_number::text)
                        )
                      AND src.id IS DISTINCT FROM v_current_id
                    ORDER BY src.id DESC
                    LIMIT 1;
                    
                    IF FOUND THEN
                        new.doc_date := COALESCE(new.doc_date, temp_record.doc_date);
                        new.buyer_name := COALESCE(new.buyer_name, temp_record.buyer_name);
                        new.buyer_code := COALESCE(new.buyer_code, temp_record.buyer_code);
                    END IF;
                END IF;
    
                -- 8. Заполнение пустого buyer_code
                IF NEW.buyer_code IS NULL AND NEW.buyer_name IS NOT NULL AND NEW.buyer_name <> '' THEN
                    SELECT buyer_code INTO temp_buyer_code
                    FROM (
                        SELECT buyer_code, COUNT(*) AS usage_count
                        FROM t_scan_documents
                        WHERE buyer_name = NEW.buyer_name
                          AND buyer_code IS NOT NULL
                          AND buyer_code <> ''
                          AND id IS DISTINCT FROM v_current_id
                        GROUP BY buyer_code
                        ORDER BY usage_count DESC, buyer_code
                        LIMIT 1
                    ) most_used;
                    
                    NEW.buyer_code := temp_buyer_code;
                END IF;
    
                -- 9. Нормализация buyer_name
                IF NEW.buyer_code IS NOT NULL THEN
                    -- Пытаемся найти непустое имя для этого кода
                    SELECT buyer_name INTO temp_buyer_name
                    FROM t_scan_documents
                    WHERE buyer_code = NEW.buyer_code
                      AND buyer_name IS NOT NULL
                      AND buyer_name <> ''
                      AND id IS DISTINCT FROM v_current_id
                    ORDER BY id DESC
                    LIMIT 1;
                    
                    IF temp_buyer_name IS NOT NULL AND temp_buyer_name <> '' THEN
                        NEW.buyer_name := temp_buyer_name;
                    END IF;
                END IF;
    
                EXCEPTION
                    WHEN OTHERS THEN
                        RAISE EXCEPTION 'Trigger error for doc_type=%, page_type=%, file=%: %', 
                            new.doc_type, new.page_type, new.file_name, SQLERRM;
            END;
    
            --10. Извлекаем из номера только цифры
            IF NEW.doc_number IS NOT NULL THEN
                new.doc_number := (
                    SELECT regexp_replace(
                        regexp_replace(
                            CASE 
                                WHEN new.doc_number LIKE '%3000%' THEN REPLACE(new.doc_number, '3000', '')
                                ELSE new.doc_number
                            END,
                            '^.*?(\d+)\D*$',
                            '\1'
                        ),
                        '^0+(\d+)$',
                        '\1'
                    )
                );
            END IF;
    
            RETURN NEW;
        END;
    $function$;                
"""
            )
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_function_base: {e}")


async def create_trigger(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
                f"""
                CREATE TRIGGER trg_{TABLE_NAME}_bfr
                BEFORE INSERT OR UPDATE ON {TABLE_NAME}
                FOR EACH ROW
                EXECUTE PROCEDURE fn_t_scan_documents();
    """
            )
    except asyncpg.exceptions.DuplicateObjectError:
        pass
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_trigger: {e}")


async def create_trigger_base(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
                f"""
                CREATE TRIGGER trg_{TABLE_NAME_RAW}_bfr
                BEFORE INSERT OR UPDATE ON {TABLE_NAME_RAW}
                FOR EACH ROW
                EXECUTE PROCEDURE fn_extract_file_name();
    """
            )
    except asyncpg.exceptions.DuplicateObjectError:
        pass
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_trigger: {e}")


async def insert_from_extract_info(pool, json_string, full_path, path, date_parse):
    """
    Добавляет данные из формата extract_info в таблицу

    Args:
        pool: Пул соединений с базой данных
        json_string: Данные в формате extract_info
        full_path: Имя файла
        path: Путь к файлу
        date_parse: Дата парсинга
    """
    try:
        data_to_insert = []

        # Пытаемся распарсить JSON, если json_string вернул строку
        extract_data = {}
        if isinstance(json_string, str):
            import json
            try:
                logger.info(f"Получена строка JSON длиной {len(json_string)} символов")
                json_string = json_string.strip()

                # Обрабатываем различные форматы JSON
                if json_string.startswith('```json'):
                    logger.info("Обнаружен формат markdown JSON")
                    json_string = json_string[7:]
                if json_string.endswith('```'):
                    json_string = json_string[:-3]
                if json_string.startswith('"'):
                    logger.info("Обнаружены внешние кавычки")
                    json_string = json_string[1:]
                if json_string.endswith('"'):
                    json_string = json_string[:-1]

                # Выводим первые 200 символов для отладки
                logger.info(f"Подготовленная JSON строка (первые 200 символов): {json_string[:200]}...")

                # Преобразуем строку JSON в словарь Python
                extract_data = json.loads(json_string)
                logger.info(f"JSON успешно преобразован в словарь: {list(extract_data.keys())}")
            except json.JSONDecodeError as e:
                logger.error(f"Не удалось распарсить JSON {json_string} из extract_info: {e}")
                logger.error(f"Первые 200 символов строки: {json_string[:200]}...")
                logger.error(f"Последние 200 символов строки: {json_string[-200:] if len(json_string) > 200 else json_string}")
        elif isinstance(json_string, dict):
            logger.info("Получен словарь вместо строки JSON")
            extract_data = json_string

        # Проверяем различные форматы данных, которые может вернуть extract_info
        if "doc" in extract_data and isinstance(extract_data["doc"], list):
            for doc in extract_data["doc"]:
                # Преобразуем invoices_numbers в строку, если это список
                invoices_str = None
                if "invoices_numbers" in doc and isinstance(doc["invoices_numbers"], list):
                    invoices_str = ",".join(map(str, doc["invoices_numbers"]))

                # Для каждой страницы в rows_list создаем запись
                for page_num in doc.get("rows_list", []):
                    # Создаем doc_key из типа документа и номера
                    doc_key = f"{doc.get('doc_type', '')}{doc.get('doc_number', '')}"

                    # Формируем кортеж данных для вставки
                    record = (
                        doc.get("doc_type"),
                        doc.get("doc_date"),
                        str(doc.get("doc_number")),
                        str(doc.get("page_type")),
                        page_num,
                        None,  # sort
                        full_path,
                        None,  # description
                        path,
                        date_parse,
                        doc_key,
                        None,  # date_from_1c
                        doc.get("buyer_name"),
                        str(doc.get("buyer_code")),
                        invoices_str,
                        doc.get("ID", 0)  # external_id
                    )
                    data_to_insert.append(record)

        # Проверяем другой возможный формат данных
        elif "documents" in extract_data and isinstance(extract_data["documents"], list):
            for doc in extract_data["documents"]:
                # Создаем doc_key из типа документа и номера
                doc_key = f"{doc.get('doc_type', '')}{doc.get('doc_number', '')}"

                # Получаем информацию о покупателе
                buyer_name = None
                buyer_code = None
                if "buyer" in extract_data:
                    buyer_name = extract_data["buyer"].get("name")
                    buyer_code = extract_data["buyer"].get("code")

                # Формируем кортеж данных для вставки
                record = (
                    doc.get("doc_type"),
                    doc.get("doc_date"),
                    str(doc.get("doc_number")),
                    extract_data.get("page_type"),
                    doc.get("page", 0),  # используем page из документа или 0
                    None,  # sort
                    full_path,
                    None,  # description
                    path,
                    date_parse,
                    doc_key,
                    None,  # date_from_1c
                    buyer_name,
                    str(buyer_code) if buyer_code else None,
                    None,  # invoices_numbers
                    doc.get("ID", 0)  # external_id
                )
                data_to_insert.append(record)

        if data_to_insert:
            async with pool.acquire() as conn:
                # Счетчики для статистики
                inserted_count = 0
                updated_count = 0

                for record in data_to_insert:
                    # Проверяем, существует ли запись с таким external_id
                    external_id = record[-1]  # Последний элемент в кортеже - external_id
                    existing_record = await conn.fetchrow(
                        f"SELECT id FROM {TABLE_NAME} WHERE external_id = $1",
                        external_id
                    )

                    if existing_record:
                        # Если запись существует, обновляем её
                        await conn.execute(
                            f"""
                            UPDATE {TABLE_NAME}
                            SET
                                doc_type = $1,
                                doc_date = CASE WHEN $2 IS NULL THEN NULL ELSE to_timestamp($2, 'DD.MM.YYYY')::date END,
                                doc_number = $3,
                                page_type = $4,
                                page = $5,
                                sort = $6,
                                full_path = $7,
                                description = $8,
                                path = $9,
                                date_parse = $10,
                                doc_key = $11,
                                date_from_1c = $12,
                                buyer_name = $13,
                                buyer_code = $14,
                                invoices_numbers = $15
                            WHERE external_id = $16
                            """,
                            *record
                        )
                        updated_count += 1
                    else:
                        # Если записи нет, вставляем новую
                        await conn.execute(
                            f"""
                            INSERT INTO {TABLE_NAME}
                            (
                                doc_type,
                                doc_date,
                                doc_number,
                                page_type,
                                page,
                                sort,
                                full_path,
                                description,
                                path,
                                date_parse,
                                doc_key,
                                date_from_1c,
                                buyer_name,
                                buyer_code,
                                invoices_numbers,
                                external_id
                            )
                            VALUES ($1,
                                   CASE WHEN $2 IS NULL THEN NULL ELSE to_timestamp($2, 'DD.MM.YYYY')::date END,
                                   $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
                            """,
                            *record
                        )
                        inserted_count += 1

                logger.info(f"Обработано {len(data_to_insert)} записей из extract_info: добавлено {inserted_count}, обновлено {updated_count}")
                return True
        else:
            logger.warning("Нет данных для добавления из extract_info")
            return False

    except Exception as e:
        logger.error(f"Ошибка при вставке данных из extract_info: {e}")
        raise ValueError(f"Ошибка при вставке данных из extract_info: {e}")


async def check_pages_in_db(pool, correct_page_numbers: list):
    # проверяем все ли страницы занесены в базу
    try:
        async with pool.acquire() as conn:
            for i, page in enumerate(correct_page_numbers):
                doc_key = list(correct_page_numbers[i].values())[0]
                page_number = list(correct_page_numbers[i].values())[1]
                query = f"SELECT COUNT(*) FROM {TABLE_NAME} WHERE doc_key = $1 AND page = $2"
                result = await conn.fetchval(query, doc_key, page_number)
                if result == 0:
                    logger.error(
                        f"Страница {page_number} файла {doc_key} не занесена в базу"
                    )
                    logger.error(f"doc_key: {doc_key}, page_number: {page_number}")
                    raise ValueError("Не все страницы занесены в базу")

            return True

    except Exception as e:
        logger.error(f"Ошибка при проверке страниц в базе: {e}")
        raise ValueError(f"Ошибка при проверке страниц в базе: {e}")


async def create_table_scan_all_pages_in_one_cell(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
                f"""
                -- Таблица для хранения информации о сканированных документах
                CREATE TABLE IF NOT EXISTS t_scan_all_pages_in_one_cell (
                    id SERIAL PRIMARY KEY,
                    doc_type varchar(255) NULL,
                    doc_date date NULL,
                    doc_number varchar(255) NULL,
                    buyer_name varchar(55) NULL,
                    buyer_code varchar(25) NULL,
                    page_numbers jsonb NULL,
                    invoices_numbers jsonb NULL,
                    amount_with_vat numeric(15,2) NULL DEFAULT 0,
                    reasoning_content TEXT NULL,
                    file_name varchar(255) NULL,
                    full_path varchar(255) NULL,
                    description TEXT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
                
                COMMENT ON TABLE t_scan_all_pages_in_one_cell IS 'Таблица для хранения информации о сканированных документах. Страницы объединены в ячейку.';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.doc_type IS 'Тип';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.doc_number IS 'Номер';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.doc_date IS 'Дата';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.buyer_name IS 'Покупатель';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.buyer_code IS 'ОКПО';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.page_numbers IS 'Список номеров страниц';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.invoices_numbers IS 'Список номеров связанных ВН';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.amount_with_vat IS 'Сумма с НДС';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.reasoning_content IS 'логика мышления';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.file_name IS 'Имя файла';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.full_path IS 'Полный путь к файлу';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.description IS 'текст страницы';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.created_at IS 'Дата создания записи';
                """
                )
            
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_tables: {e}")


async def create_tables_base(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
                f"""
                -- Таблица для хранения информации о сканированных документах
                CREATE TABLE IF NOT EXISTS {TABLE_NAME_RAW} (
                    id SERIAL PRIMARY KEY,
                    page_number INT NOT NULL,
                    file_name varchar(100) NOT NULL,
                    full_path varchar(255) NOT NULL,
                    description TEXT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    CONSTRAINT file_name_page_number_unq UNIQUE (file_name, page_number)
                );

                COMMENT ON TABLE {TABLE_NAME_RAW} IS 'Текст отсканированных страниц';
                COMMENT ON COLUMN {TABLE_NAME_RAW}.full_path IS 'Полный путь к файлу';
                COMMENT ON COLUMN {TABLE_NAME_RAW}.page_number IS 'номер страницы';
                COMMENT ON COLUMN {TABLE_NAME_RAW}.description IS 'текст страницы';
                COMMENT ON COLUMN {TABLE_NAME_RAW}.created_at IS 'Дата создания записи';
                """
            )
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_tables: {e}")


async def insert_document_base(pool, data: list):
    try:
        async with pool.acquire() as conn:
            await conn.executemany(
                f"""
                INSERT INTO {TABLE_NAME_RAW}
                (
                    full_path,
                    page_number,
                    description,
                    created_at
                )
                VALUES ($1, $2, $3, now())
                ON CONFLICT (file_name, page_number) DO UPDATE
                SET
                    description = EXCLUDED.description,
                    page_number = EXCLUDED.page_number,
                    full_path = EXCLUDED.full_path,
                    created_at = now()
                """,
                data,
            )
    except Exception as e:
        logger.error(f"Ошибка при вставке данных в базу: {e}")
        raise ValueError(f"Ошибка при вставке данных в базу: {e}")


async def main_database(drop_raw_table=False):
    """
    Инициализирует базу данных

    Args:
        drop_raw_table: Если True, удаляет таблицу t_scan_documents_raw
    """
    pool = await create_pool()
    if pool:
        async with pool.acquire() as conn:
            # Устанавливаем SIMILAR
            await conn.execute(SIMILAR)
            await conn.execute("CREATE EXTENSION IF NOT EXISTS unaccent;")           

            # Удаляем таблицу t_scan_documents
            # await conn.execute(f"DROP TABLE IF EXISTS {TABLE_NAME};")
            await create_function(pool)
            await create_function_base(pool)

            # Создаем таблицы
            await create_tables(pool)
            await create_tables_base(pool)
            
            # Создаем триггеры
            await create_trigger(pool)
            await create_trigger_base(pool)

            # Удаляем таблицу t_scan_documents_raw только если указано
            # if drop_raw_table:
            #     logger.warning("Удаление таблицы t_scan_documents_raw")
            # НЕ удалять
                # await conn.execute(f"DROP TABLE IF EXISTS {TABLE_NAME_RAW};")

        await pool.close()


async def process_raw_documents(pool):
    """
    Обрабатывает документы из таблицы t_scan_documents_raw

    Args:
        pool: Пул соединений с базой данных
    """
    try:
        # Импортируем функцию main из process_documents.py
        # Используем переданный пул соединений
        from process_documents import process_with_pool
        await process_with_pool(pool)
        return True
    except Exception as e:
        logger.error(f"Ошибка при обработке документов: {e}")
        return False


async def main_database_process():
    """Запускает процесс обработки документов из raw таблицы"""
    pool = await create_pool()
    if pool:
        try:
            await process_raw_documents(pool)
        finally:
            await pool.close()

if __name__ == "__main__":
    # Для тестирования можно запустить обработку документов
    # asyncio.run(main_database_process())
    asyncio.run(main_database())