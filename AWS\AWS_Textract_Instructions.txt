Инструкция для администратора AWS по настройке доступа к AWS Textract

Для работы с AWS Textract через скрипт AWS_OCR.py необходимо добавить соответствующие разрешения для пользователя.

1. Войдите в консоль AWS (https://console.aws.amazon.com/) под учетной записью с правами администратора.

2. Перейдите в сервис IAM (Identity and Access Management):
   - В поиске сервисов введите "IAM" или найдите его в списке сервисов

3. Добавьте политику к пользователю:
   - В левом меню выберите "Пользователи" (Users)
   - Найдите и выберите пользователя "Rasim"
   - Перейдите на вкладку "Разрешения" (Permissions)
   - Нажмите кнопку "Добавить разрешения" (Add permissions)
   - Выберите "Прикрепить политики напрямую" (Attach policies directly)
   - В поиске введите "Textract"
   - Отметьте политику "AmazonTextractFullAccess"
   - Нажмите "Далее" и затем "Добавить разрешения"

4. После добавления разрешений, пользователь сможет использовать AWS Textract через скрипт AWS_OCR.py.

Примечание: Изменения в IAM могут занять несколько минут, прежде чем вступят в силу.
