# Ограничение частоты вызовов Gemini API

## Описание изменений

В проект добавлены файлы для ограничения частоты вызовов Gemini API до 14 запросов в минуту:

1. `gemini_limiter.py` - содержит класс `GeminiRateLimiter` для ограничения частоты запросов
2. `process_documents_modified.py` - модифицированная версия основного файла с использованием ограничителя

## Как использовать

Для использования ограничителя частоты запросов:

1. Скопируйте содержимое функции `process_one_batch_async` из файла `process_documents_modified.py` в ваш основной файл `process_documents.py`
2. Добавьте импорт в начало файла: `from gemini_limiter import GeminiRateLimiter`
3. Добавьте создание глобального ограничителя: `gemini_limiter = GeminiRateLimiter(max_calls=14, period=60)`
4. Перед вызовом `extract_data_by_gemini_flash` добавьте строку: `await gemini_limiter.acquire()`

## Принцип работы

Ограничитель использует механизм "скользящего окна" для отслеживания вызовов API. Когда достигается лимит в 14 запросов за 60 секунд, последующие вызовы будут ожидать, пока не освободится место в окне.